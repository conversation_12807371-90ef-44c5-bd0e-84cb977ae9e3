/**
 * 方块生成器
 * 移植自原LodingCube逻辑
 */
import { ICube } from "../models/ICube";
import EnumHelper from "../helpers/EnumHelper";

export class CubeGenerator {
    /**
     * 生成单个方块
     */
    static generateSingle(cubeArrayLength: number, allowBone: boolean): ICube {
        const length = this.validateArrayLength(cubeArrayLength);
        let color = Math.floor(Math.random() * length);

        if (!allowBone && color === 0) {
            color++;
        }

        return {
            color,
            statics: 0,
            eff: EnumHelper.Effects.nullEff
        };
    }

    /**
     * 批量生成方块
     */
    static generateMultiple(cubeArrayLength: number, allowBone: boolean, count: number = 1): ICube[] {
        const results: ICube[] = [];
        const length = this.validateArrayLength(cubeArrayLength);

        for (let i = 0; i < count; i++) {
            let color = Math.floor(Math.random() * length);
            if (!allowBone && color === 0) {
                color++;
            }
            results.push({
                color,
                statics: 0,
                eff: EnumHelper.Effects.sixEff
            });
        }
        return results;
    }

    /**
     * 验证并修正方块数组长度
     */
    private static validateArrayLength(length: number): number {
        return Math.max(
            EnumHelper.CommonEnum.minCubeArrayLength,
            Math.min(length, EnumHelper.CommonEnum.maxCubeArrayLength)
        );
    }
}