/**
 * 粒子效果辅助类
 * 移植自原ParticalHelper.js
 * <AUTHOR>
 */

import { _decorator, Node, ParticleSystem, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

interface IParticleParams {
    layer: Node;
    widget: Node;
    particalName: string;
    scaleF: number;
}

export default class ParticalHelper {
    /**
     * 添加星星粒子效果
     * @param params 粒子参数
     */
    static addStar(params: IParticleParams): void {
        // TODO: 实现粒子效果
        // const { layer, widget, particalName, scaleF } = params;
        // const particle = layer.addComponent(ParticleSystem);
        // particle.file = particalName;
        // particle.node.position = widget.position.clone();
        // particle.node.scale = new Vec3(scaleF, scaleF, 1);
    }
}