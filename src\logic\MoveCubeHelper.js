/**
 * Created by <PERSON><PERSON> on 15/9/3.
 */

var MoveCubeHelper = {
    findNeedCobeAndMove: function (jingdian) {

    },
    findTopCube: function (jingdian, list, index) {
        var rtnList = [];   //std::vector<int>
        //从arr中获取 i 以上不为 0 的元素, 并剔除list中的元素
        var arr = jingdian._arr;
        for (var i = index; i < arr.length; i = i + EnumHelper.ChessBoard.ChessWidth) {
            if (arr[i] == null) {
                continue;
            }
            var isAdd = true;
            for (var j = 0; j < list.length; j++) {
                if (i == list[j]) {
                    isAdd = false;
                    break;
                }
            }

            if (isAdd) {
                rtnList.push(i);
            }

        }
        return rtnList;
    },
    moveMatrix: function (jingdian) {
        /*
         给loyout 中的所有900+i的色块加上向上移动的action
         给800+i的所有色块机上向上移动的action
         */
        var arr = jingdian._arr;
        var bottomeCube = jingdian._bottomCube;
        var layout = jingdian._root;

        if (bottomeCube.length < EnumHelper.ChessBoard.ChessWidth) {
            return;
        }

        var cube;
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] != null) {
                cube = ccui.helper.seekWidgetByTag(layout, 900 + i);
                if (cube != null) {
                    cube.setTag(cube.getTag() + EnumHelper.ChessBoard.ChessWidth);
                }
            }
        }

        for (i = 0; i < bottomeCube.length; i++) {
            cube = ccui.helper.seekWidgetByTag(layout, 800 + i);
            if (cube != null) {
                cube.setTag(cube.getTag() + 100);
            }
        }
    }
};