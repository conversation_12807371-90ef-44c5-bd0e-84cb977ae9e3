/**
 * Created by <PERSON><PERSON> on 15/8/23.
 */

var AnimationHelper = {
    addBaozha: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "baozha", visible, scaleF,  zOrder);
    },
    addClude: function (layer, widget, index, visible) {
        if (widget != null)
        {
            var armature = new ccs.Armature("CubeAnimation");
            armature.getAnimation().play("cloud");
            armature.setPosition(widget.getContentSize().width / 2, widget.getContentSize().height / 2);
            armature.setTag(600 + index);
            armature.setScale(3);
            armature.setVisible(visible);

            widget.addChild(armature, widget.getGlobalZOrder()+1);
        }
    },
    addStar: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "star", visible, scaleF, zOrder);
    },
    addClearCube: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "CubeClear", visible, scaleF,zOrder);
    },
    addFire: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "fire", visible, scaleF, zOrder);
    },
    addSpot: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "spot", visible, scaleF, zOrder);
    },
    addRainbow: function (layer, widget, index, visible, scaleF, zOrder) {
        this.createWithNotEvent(layer, widget, index, "rainbow", visible, scaleF, zOrder);
    },
    addBroken: function (layer, widget, index) {
        this.create(layer, widget, index, "broken");
    },
    addClick: function (layer, widget, index) {
        this.create(layer, widget, index, "click");
    },
    addTest: function (layer, widget, index) {
        this.create(layer, widget, index, "test");
    },
    createWithNotEvent: function (layer, widget, index, AnimationName, visible, scaleF, zOrder) {
        if (widget != null)
        {
            var armature = new ccs.Armature("CubeAnimation");
            armature.getAnimation().play(AnimationName);
            armature.setPosition(widget.getContentSize().width / 2, widget.getContentSize().height / 2);
            armature.setTag(600 + index);
            armature.setVisible(visible);
            armature.setScale(scaleF);
            widget.addChild(armature, zOrder);
        }
    },
    create: function (layer, widget, index, AnimationName){
        if (widget != null)
        {
            var armature = new ccs.Armature("CubeAnimation");

            armature.getAnimation().play(AnimationName);
            armature.setPosition(widget.getContentSize().width / 2, widget.getContentSize().height / 2);
            armature.setTag(600 + index);
            //armature.getAnimation().setMovementEventCallFunc(CC_CALLBACK_0(Jingdian.onStartEvent, layer, std.placeholders._1, std.placeholders._2, std.placeholders._3));

            widget.addChild(armature);
        }
    }
};
