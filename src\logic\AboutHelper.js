/**
 * Created by <PERSON><PERSON> on 15/8/23.
 */

var AboutHelper = {
    addChildCube: function (widget, cubeName) {
        if (widget != null) {
            var imageview = new ccui.ImageView(cubeName, ccui.Widget.PLIST_TEXTURE);
            imageview.setPosition(widget.getContentSize().width / 2, widget.getContentSize().height / 2);
            widget.addChild(imageview);
            return imageview;
        }
    },
    action_Three_HaveCallBack: function (widget, layer) {
        var actImg = this.addChildCube(widget, RES.GET_DOG(10));
        actImg.setOpacity(0);
        var suq = cc.sequence(
            cc.fadeTo(0.1, 255),
            cc.callFunc(layer.threeCallBack, layer));
        actImg.runAction(suq);
    },
    action_Three_NoCallBack: function (widget) {
        var actImg = this.addChildCube(widget, RES.GET_DOG(10));
        actImg.setOpacity(0);
        var suq = cc.sequence(
            cc.fadeTo(0.1, 255)
            //Blink.create(0.5, 0),//加眨眼效果
        );
        actImg.runAction(suq);
    },
    action_four_HaveCallBack: function (widget, tagetWidget, layer) {
        var act = cc.sequence(
            cc.moveTo(0.1, tagetWidget.getPosition()),
            cc.callFunc(layer.threeCallBack, layer)
        );
        widget.runAction(act);
    },
    action_four_NoCallBack: function (widget, tagetWidget, layer) {
        var act = cc.sequence(
            cc.moveTo(0.1, tagetWidget.getPosition()),
            cc.fadeTo(0.01, 0)
        );
        widget.runAction(act);
    },
    action_five_HaveCallBack: function (tag, layer) {
        //获取矩阵第一个位置
        var widget = ccui.helper.seekWidgetByTag(layer._root, tag);

        var startCube = ccui.helper.seekWidgetByTag(layer._root, EnumHelper.MainCommonEnum.startPoint);
        var position = startCube.getPosition();

        var height = startCube.getContentSize().height * startCube.getScale();
        var width = startCube.getContentSize().width * startCube.getScale();

        var y = position.y - height / 2 + height * (EnumHelper.ChessBoard.ChessHeight / 2);
        var x = position.x - width / 2 + width * EnumHelper.ChessBoard.ChessWidth / 2;

        var imgV = new ccui.ImageView(RES.DOG_LIGH_V, ccui.Widget.PLIST_TEXTURE);
        //竖着的 x 应该是在 第四行和第五行之间  ,横着的 y应该是第四列中央

        var scaleY = height * EnumHelper.ChessBoard.ChessHeight / imgV.getContentSize().height;
        imgV.setScale(scaleY);
        imgV.setPosition(widget.getWorldPosition().x, y);
        layer._playLayout.addChild(imgV);
        var act = cc.sequence(
                cc.scaleBy(0.1, 1.05),
                cc.scaleBy(0.5, 0.5, 0.9),
                cc.callFunc(layer.fiveCallBack, layer)
            );
        imgV.runAction(act);

        var imgAcross = new ccui.ImageView(RES.DOG_LIGH_H, ccui.Widget.PLIST_TEXTURE);
        imgAcross.setScale(scaleY);
        imgAcross.setPosition(x, widget.getPosition().y);
        layer._playLayout.addChild(imgAcross);

        //先显示出来,  然后快速变窄消失掉, 调用函数,在函数中执行下一帧删除
        var action = cc.sequence(
            cc.scaleBy(0.1, 1.05),
            cc.scaleBy(0.5, 0.9, 0.5)
        );
        imgAcross.runAction(action);
    },
    action_five_NoCallBack: function (layer, i) {
        //加白色斑点的粒子特效
        var img = ccui.helper.seekWidgetByTag(layer._root, 900 + i);
        if (img) {
            ParticalHelper.addStar(layer, img, res.SnowParticle_plist, 2.5);
        }
    },
    find_fivePassively: function (i, arr, list, layer) {
        var index;
        var inList;

        for (index = 0; index < list.length; index++) {
            if (list[index] == i) {
                return;
            }
        }

        //给i加上特效
        this.action_five_HaveCallBack(900 + i, layer);

        var row = i / EnumHelper.ChessBoard.ChessWidth;
        for (var a = EnumHelper.ChessBoard.ChessWidth * row; a < (row + 1) * EnumHelper.ChessBoard.ChessWidth; a++) {
            if (arr[a] != null) {
                inList = false;
                //判断a是否已在list中
                for (index = 0; index < list.length; index++) {
                    if (list[index] == a) {
                        inList = true;
                    }
                }
                if (inList) {
                    continue;
                }

                if (arr[a] != null) {
                    list.push(a);
                    if (arr[a].eff == EnumHelper.Effects.fiveEff) {
                        this.find_fivePassively(a, arr, list, layer);
                    }
                }
            }
        }

        var column = i % EnumHelper.ChessBoard.ChessWidth;
        while (column < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight) {
            if (arr[column] != null) {
                inList = false;
                //判断a是否已在list中
                for (index = 0; index < list.length; index++) {
                    if (list[index] == column) {
                        inList = true;
                    }
                }
                if (inList) {
                    column += EnumHelper.ChessBoard.ChessWidth;
                    continue;
                }

                if (arr[column] != null) {
                    list.push(column);
                    if (arr[column].eff == EnumHelper.Effects.fiveEff) {
                        this.find_fivePassively(column, arr, list, layer);
                    }
                }
            }
            column += EnumHelper.ChessBoard.ChessWidth;
        }
    },
    action_moveCube_HaveCallBack: function (second, widget, v, layer) {
        var act = cc.sequence(
            cc.moveTo(second, v),
            cc.callFunc(layer.moveCube_ActionCallBack, layer)
        );
        widget.runAction(act);
    },
    action_moveCube_NoHaveCallBack: function (second, widget, v) {
        var act = cc.sequence(
            cc.moveTo(second, v)
        );
        widget.runAction(act);
    },
    action_moveCube_Decline_HaveCallBack: function (widget, v, layer) {
        var act = cc.sequence(
            cc.moveTo(0.1, v),
            cc.callFunc(layer.moveCube_Decline_CallBack, layer)
        );
        widget.runAction(act);
    },
    action_moveCube_Decline_NoCallBack: function (widget, v) {
        var act = cc.sequence(
            cc.moveTo(0.1, v)
        );
        widget.runAction(act);
    }
};