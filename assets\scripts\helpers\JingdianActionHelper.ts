/**
 * 经典模式动作辅助类
 * 移植自原JingdianActionHelper.js
 * <AUTHOR>
 */

import { _decorator, Component, Node, Sprite, tween, Vec3, UITransform } from 'cc';
import EnumHelper from './EnumHelper';
import { RES } from '../utils/ResourceManager';
const { ccclass, property } = _decorator;

interface IGameLayer {
    _root: Node;
    _hint: Node;
    clickActionCallBack?: () => void;
    readyGoCallBack?: () => void;
}

export default class JingdianActionHelper {
    /**
     * 点击动作
     * @param sender 触发节点
     * @param jingdian 游戏实例
     */
    static clickAction(sender: Node, jingdian: IGameLayer) {
        const will1 = jingdian._root.getChildByName('willImg1');
        if (!will1 || !sender) return;

        const targetPos = sender.position.clone();
        tween(will1)
            .to(0.1, { position: targetPos })
            .call(() => {
                if (jingdian.clickActionCallBack) {
                    jingdian.clickActionCallBack();
                }
            })
            .start();
    }

    /**
     * 提示动作方法
     * @param layer 游戏层
     * @param type 动作类型
     */
    static hintActionMethod(layer: IGameLayer, type: number) {
        if (!layer._hint) return;

        layer._hint.active = true;
        const uiTransform = layer._hint.getComponent(UITransform);
        if (!uiTransform) return;

        // 重置位置
        layer._hint.position = new Vec3(
            uiTransform.contentSize.width / 2,
            uiTransform.contentSize.height / 2,
            0
        );

        // 停止所有动作
        tween(layer._hint).stop();

        // 执行移动动作
        tween(layer._hint)
            .to(3, {
                position: new Vec3(
                    uiTransform.contentSize.width / 2,
                    uiTransform.contentSize.height / 2 + 100,
                    0
                )
            })
            .start();
    }

    /**
     * 提示动作
     * @param layer 游戏层
     * @param type 动作类型
     */
    static hintAction(layer: IGameLayer, type: number) {
        if (!layer._hint) return;

        const labelAtlas = layer._hint.getChildByName('hintLabelAtlas');
        if (labelAtlas) labelAtlas.active = false;

        const img = layer._hint.getChildByName('hintImg')?.getComponent(Sprite);
        if (!img) return;

        switch (type) {
            case EnumHelper.ActionType.comboHint:
                if (labelAtlas) labelAtlas.active = true;
                img.spriteFrame = RES.HINT_COMBO_OFF;
                this.hintActionMethod(layer, EnumHelper.ActionType.comboHint);
                break;
            case EnumHelper.ActionType.readyHint:
                img.spriteFrame = RES.HINT_READY;
                this.hintActionMethod(layer, EnumHelper.ActionType.readyHint);
                break;
            case EnumHelper.ActionType.goHint:
                img.spriteFrame = RES.HINT_GO;
                this.hintActionMethod(layer, EnumHelper.ActionType.goHint);
                break;
            default:
                break;
        }
    }

    /**
     * 准备/开始动作
     * @param layer 游戏层
     * @param type 动作类型
     */
    static readyGoAction(layer: IGameLayer, type: number) {
        const panel = layer._root.getChildByName('readyGoPanel');
        if (!panel) return;

        panel.active = true;
        const img = panel.getChildByName('readyGoImg')?.getComponent(Sprite);
        if (!img) return;

        switch (type) {
            case EnumHelper.ActionType.readyHint:
                img.spriteFrame = RES.HINT_READY;
                this.readyGoActionMethods(layer, 1);
                break;
            case EnumHelper.ActionType.goHint:
                img.spriteFrame = RES.HINT_GO;
                this.readyGoActionMethods(layer, 0.5);
                break;
            default:
                break;
        }
    }

    /**
     * 准备/开始动作方法
     * @param layer 游戏层
     * @param second 动画时长
     */
    static readyGoActionMethods(layer: IGameLayer, second: number) {
        const img = layer._root.getChildByName('readyGoImg');
        if (!img) return;

        tween(img)
            .to(second, { scale: new Vec3(1.2, 1.2, 1) })
            .call(() => {
                if (layer.readyGoCallBack) {
                    layer.readyGoCallBack();
                }
            })
            .start();
    }

    /**
     * 连击动作
     * @param layer 游戏层
     */
    static comboAction(layer: IGameLayer) {
        this.hintAction(layer, EnumHelper.ActionType.comboHint);
    }

    /**
     * 箭头上移动画
     * @param layer 游戏层
     */
    static arrowsUpAction(layer: IGameLayer) {
        const arrowsUp = layer._root.getChildByName('arrowsUpLayout');
        if (!arrowsUp) return;

        const originalPos = arrowsUp.position.clone();
        arrowsUp.active = true;

        tween(arrowsUp)
            .to(0.5, { position: new Vec3(originalPos.x, originalPos.y + 50, 0) })
            .call(() => {
                arrowsUp.position = originalPos;
                arrowsUp.active = false;
            })
            .start();
    }
}