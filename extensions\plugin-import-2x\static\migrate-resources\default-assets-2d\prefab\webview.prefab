[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "webview", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 1, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "_localZOrder": 55, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}}, {"__type__": "cc.WebView", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_useOriginalSize": false, "_url": "", "webviewEvents": [], "_id": "12QHFUfdhF+4w/zU5gB7ZI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8c5001fd-07ee-4a4b-a8a0-63e15195e94d"}, "fileId": "2fb356PwclC8L2CbHhqGUVr", "sync": false}]