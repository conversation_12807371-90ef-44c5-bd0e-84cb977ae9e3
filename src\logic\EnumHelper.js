/**
 * Created by <PERSON><PERSON> on 15/8/22.
 */

var EnumHelper = {
    RED:{r:255, g:0, b:0},
    BLACK: {r:0, g:0, b:0},
    GREEN:{r:0, g:255, b:0},

    MainMenuEnum : {
        settingBtn : 10,
        rankListBtn : 12,
        shopBtn : 14,
        jingdianBtn : 4,
        shuangxiaoBtn : 6,
        tiaozhanBtn : 8
    },

    SettingEnum : {
        backBtnSetting : 56,
        musicBtn : 51,
        musicImg : 48,
        soundBtn : 50,
        soundImg : 52
    },

    ShopEnum  : {
        Button_shop_back : 74,
        Button_shop_one : 92
    },

    RankListEnum : {
        btn_back : 197,
        scv_ranklist : 251
    },

    RankListItemEnum : {
        pnl_bg : 1424,
        img_bg : 1482,
        img_lv : 1469,
        fnt_lv : 1470,
        txt_date : 1471,
        img_cup : 1472,
        txt_score : 1473,
        fnt_rank : 1474,
        img_rank : 1497
    },

    jingdianEnum : {
        jingdianMainPanelBg : 363,
        fenshu<PERSON><PERSON>l : 557,
        levelLabelAtlas : 366
    },

    shuangxiaoEnum : {},

    tiaozhanEnum : {},

    gameOverEnum : {
        aginBtn_gameOver : 592,
        escBtn_gameOver : 594,
        rankList_gameOver : 597,

        maxLevel_Text_gameOver : 950,// 604,
        mexScore_gameOver : 952,
        level_gameOver : 957,
        combo_gameOver : 958,
        score_gameOver : 959,
        newImg_gameOver : 1247,

        cupbg_gameOver : 1601,
        goldCup_gameOver : 1602
    },

    MainCommonEnum : {
        pauseBtn : 370,
        propsImg1 : 378,
        propsCount1 : 410,
        propsImg2 : 379,
        propsCount2 : 412,
        propsImg3 : 380,
        propsCount3 : 414,

        startPoint : 554,
        loadingPoint : 546,

        willImg1 : 371,
        willImg2 : 372,
        willImg3 : 374,


        dotedLine2 : 389,
        dottedLine1 : 390,
        clockSmall : 391,
        clockBig : 392,

        timerLoadingBar : 405,
        secondImg : 406,
        secondTxt : 408,

        bottomVeil2Img : 395,
        bottomVeil1Img : 394,


        hintLayout : 1130,
        hintImg : 1132,
        hintImg2 : 100221,
        hintLabelAtlas : 1134,

        arrowsUpLayout : 1136,
        arrows1 : 1137,
        arrows2 : 1138,
        arrows3 : 1139,
        arrows4 : 1140,

        readyGoPanel : 100315,
        readyGoImg : 100317
    },


    gameMenu2 : {
        GoonGameBtn : 339,
        newGameBtn : 342,
        backBtns : 345
    },

    MainCommon2 : {
        goon_btn : 327,
        new_btn : 330,
        saveBack_Btn : 331
    },

    CubeColor : {
        Bone : 0,
        redDog : 1,
        blueDog : 2,
        greenDog : 3
    },

    Effects : {
        nullEff : 0,
        fourEff : 4,
        fiveEff : 5,
        sixEff : 6
    },

    ChessBoard : {
        ChessWidth : 7,
        ChessHeight : 8
    },

    CommonEnum : {
        score : 100,
        scoreSize : 50,
        levelRemainder : 5000,
        cupRemainder : 50000,
        maxCubeArrayLength : 8,
        minCubeArrayLength : 4,

        jingdianSecond : 30,
        shuangxiaoSecond : 30,
        tiaozhanSecond : 80,

        bottomCubeSecondSize : 1

    },

    moveType : {
        starLoadingMove : 1,
        loadingMove : 2

    },

    ActionType : {
        comboHint : 1,
        readyHint : 2,
        goHint : 3,
        levelUpHint : 4,
        startHint : 5
    },

    RankList : {
        scriptureSize : 20,
        doubleClearSize : 20,
        challengeSize : 20
    }
};