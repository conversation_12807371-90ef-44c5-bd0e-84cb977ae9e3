/**
 * 游戏枚举辅助类
 * 移植自原EnumHelper.js
 * <AUTHOR>
 */

interface ColorRGB {
    r: number;
    g: number;
    b: number;
}

export namespace Colors {
    export const RED: ColorRGB = { r: 255, g: 0, b: 0 };
    export const BLACK: ColorRGB = { r: 0, g: 0, b: 0 };
    export const GREEN: ColorRGB = { r: 0, g: 255, b: 0 };
}

export namespace MainMenu {
    export enum Elements {
        settingBtn = 10,
        rankListBtn = 12,
        shopBtn = 14,
        jingdianBtn = 4,
        shuangxiaoBtn = 6,
        tiaozhanBtn = 8
    }
}

export namespace Setting {
    export enum Elements {
        backBtnSetting = 56,
        musicBtn = 51,
        musicImg = 48,
        soundBtn = 50,
        soundImg = 52
    }
}

export namespace Shop {
    export enum Elements {
        Button_shop_back = 74,
        Button_shop_one = 92
    }
}

export namespace RankList {
    export enum Elements {
        btn_back = 197,
        scv_ranklist = 251
    }

    export enum ItemElements {
        pnl_bg = 1424,
        img_bg = 1482,
        img_lv = 1469,
        fnt_lv = 1470,
        txt_date = 1471,
        img_cup = 1472,
        txt_score = 1473,
        fnt_rank = 1474,
        img_rank = 1497
    }
}

export namespace Jingdian {
    export enum Elements {
        jingdianMainPanelBg = 363,
        fenshuLabel = 557,
        levelLabelAtlas = 366
    }
}

export namespace GameOver {
    export enum Elements {
        aginBtn_gameOver = 592,
        escBtn_gameOver = 594,
        rankList_gameOver = 597,
        maxLevel_Text_gameOver = 950,
        mexScore_gameOver = 952,
        level_gameOver = 957,
        combo_gameOver = 958,
        score_gameOver = 959,
        newImg_gameOver = 1247,
        cupbg_gameOver = 1601,
        goldCup_gameOver = 1602
    }
}

export namespace GameMenu {
    export enum Elements {
        GoonGameBtn = 339,
        newGameBtn = 342,
        backBtns = 345
    }
}

export namespace MainCommon2 {
    export enum Elements {
        goon_btn = 327,
        new_btn = 330,
        saveBack_Btn = 331
    }
}

export namespace MainCommonEnum {
    export const pauseBtn = 370;
    export const propsImg1 = 378;
    export const propsCount1 = 410;
    export const propsImg2 = 379;
    export const propsCount2 = 412;
    export const propsImg3 = 380;
    export const propsCount3 = 414;

    export const startPoint = 554;
    export const loadingPoint = 546;

    export const willImg1 = 371;
    export const willImg2 = 372;
    export const willImg3 = 374;

    export const dotedLine2 = 389;
    export const dottedLine1 = 390;
    export const clockSmall = 391;
    export const clockBig = 392;

    export const timerLoadingBar = 405;
    export const secondImg = 406;
    export const secondTxt = 408;

    export const bottomVeil2Img = 395;
    export const bottomVeil1Img = 394;

    export const hintLayout = 1130;
    export const hintImg = 1132;
    export const hintImg2 = 100221;
    export const hintLabelAtlas = 1134;

    export const arrowsUpLayout = 1136;
    export const arrows1 = 1137;
    export const arrows2 = 1138;
    export const arrows3 = 1139;
    export const arrows4 = 1140;

    export const readyGoPanel = 100315;
    export const readyGoImg = 100317;
}

export enum CubeColor {
    Bone = 0,
    redDog = 1,
    blueDog = 2,
    greenDog = 3
}

export enum Effects {
    nullEff = 0,
    fourEff = 4,
    fiveEff = 5,
    sixEff = 6
}

export namespace ChessBoard {
    export const ChessWidth = 7;
    export const ChessHeight = 8;
}

export namespace CommonEnum {
    export const score = 100;
    export const scoreSize = 50;
    export const scorePerCube = 100;
    export const minRemoveCount = 3;
    export const levelRemainder = 5000;
    export const cupRemainder = 50000;
    export const maxCubeArrayLength = 8;
    export const minCubeArrayLength = 4;
    export const jingdianSecond = 30;
    export const shuangxiaoSecond = 30;
    export const tiaozhanSecond = 80;
    export const bottomCubeSecondSize = 1;
}

export enum MoveType {
    starLoadingMove = 1,
    loadingMove = 2
}

export enum ActionType {
    comboHint = 1,
    readyHint = 2,
    goHint = 3,
    levelUpHint = 4,
    startHint = 5
}

export namespace RankListConfig {
    export const scriptureSize = 20;
    export const doubleClearSize = 20;
    export const challengeSize = 20;
}

// 默认导出整个 EnumHelper 对象，保持向后兼容
const EnumHelper = {
    Colors,
    MainMenu,
    Setting,
    Shop,
    RankList,
    Jingdian,
    GameOver,
    GameMenu,
    MainCommon2,
    MainCommonEnum,
    CubeColor,
    Effects,
    ChessBoard,
    CommonEnum,
    MoveType,
    ActionType,
    RankListConfig
};

export default EnumHelper;