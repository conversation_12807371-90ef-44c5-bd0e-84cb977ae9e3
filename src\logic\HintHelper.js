/**
 * Created by <PERSON><PERSON> on 15/9/3.
 */

//hint信息
var HintHelper = {
    setCombo: function (jingdian) {
        var hint = jingdian._hint;
        jingdian._currentCombo++;
        if (jingdian._currentCombo > jingdian._maxCombo) {
            jingdian._maxCombo = jingdian._currentCombo;
        }
        if (jingdian._currentCombo > 1) {
            hint.setVisible(true);
        }
        var leftOld = ccui.helper.seekWidgetByTag(hint, 110);
        if (leftOld) {
            leftOld.stopAllActions();
            hint.removeChildByTag(110);
        }
        var hintImg = ccui.helper.seekWidgetByTag(hint, EnumHelper.MainCommonEnum.hintImg);
        var hintAct = cc.sequence(
            cc.progressTo(3, 100),
            cc.callFunc(jingdian.comboActionOver, jingdian)
        );

        //hintLabelAtlas
        var hintLB = ccui.helper.seekWidgetByTag(hint, EnumHelper.MainCommonEnum.hintLabelAtlas);
        hintLB.setString(jingdian._currentCombo);
        hintLB.setVisible(true);

        var sp = new cc.Sprite();
        sp.setSpriteFrame(RES.HINT_COMBO_ON);
        var left = new cc.ProgressTimer(sp);
        left.setTag(110);
        left.setType(cc.ProgressTimer.TYPE_BAR);
        left.setMidpoint(cc.p(0, 0));
        left.setBarChangeRate(cc.p(1, 0));
        left.setPosition(hintImg.getPosition());
        left.runAction(hintAct);
        left.setScale(hintImg.getScale());
        left.setLocalZOrder(hintImg.getLocalZOrder() + 1);
        hint.addChild(left, 1000);
    }
};