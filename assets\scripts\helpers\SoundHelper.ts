/**
 * 音效辅助类
 * 为游戏提供音效管理功能
 * <AUTHOR>
 */

import { _decorator, AudioClip, AudioSource, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

interface ISoundConfig {
    [key: string]: {
        clip: AudioClip | null;
        volume?: number;
        loop?: boolean;
    };
}

export default class SoundHelper {
    private static _instance: SoundHelper | null = null;
    private _audioSource: AudioSource | null = null;
    private _soundConfig: ISoundConfig = {};

    /**
     * 获取单例实例
     */
    public static get instance(): SoundHelper {
        if (!SoundHelper._instance) {
            SoundHelper._instance = new SoundHelper();
        }
        return SoundHelper._instance;
    }

    /**
     * 初始化音效系统
     * @param node 挂载音频组件的节点
     */
    public initialize(node: Node): void {
        this._audioSource = node.addComponent(AudioSource);
        this._soundConfig = {};
    }

    /**
     * 预加载音效资源
     * @param soundName 音效名称
     * @param clip 音频剪辑
     * @param volume 音量(0-1)
     * @param loop 是否循环
     */
    public preloadSound(
        soundName: string, 
        clip: AudioClip, 
        volume: number = 1.0, 
        loop: boolean = false
    ): void {
        this._soundConfig[soundName] = {
            clip,
            volume,
            loop
        };
    }

    /**
     * 播放音效
     * @param soundName 音效名称
     * @param volume 音量(可选，覆盖预加载设置)
     */
    public playSound(soundName: string, volume?: number): void {
        if (!this._audioSource) {
            console.warn('AudioSource not initialized');
            return;
        }

        const config = this._soundConfig[soundName];
        if (!config || !config.clip) {
            console.warn(`Sound ${soundName} not preloaded`);
            return;
        }

        this._audioSource.clip = config.clip;
        this._audioSource.volume = volume !== undefined ? volume : (config.volume || 1.0);
        this._audioSource.loop = config.loop || false;
        this._audioSource.play();
    }

    /**
     * 停止当前播放的音效
     */
    public stopSound(): void {
        if (this._audioSource) {
            this._audioSource.stop();
        }
    }

    /**
     * 设置全局音量
     * @param volume 音量(0-1)
     */
    public setVolume(volume: number): void {
        if (this._audioSource) {
            this._audioSource.volume = volume;
        }
    }

    /**
     * 检查音效是否正在播放
     */
    public isPlaying(): boolean {
        return this._audioSource ? this._audioSource.playing : false;
    }
}