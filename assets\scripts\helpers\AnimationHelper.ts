/**
 * 动画辅助类
 * 移植自原AnimationHelper.js
 * <AUTHOR>
 */

import { _decorator, Animation, Component, Node, UITransform, Vec3, tween } from 'cc';
const { ccclass, property } = _decorator;

interface IAnimationParams {
    layer: Node;
    widget: Node;
    index: number;
    visible?: boolean;
    scaleF?: number;
    zOrder?: number;
}

export default class AnimationHelper {
    /**
     * 添加爆炸动画
     */
    static addBaozha(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "baozha"
        });
    }

    /**
     * 添加云朵动画
     */
    static addClude(params: IAnimationParams): void {
        const { widget, index, visible } = params;
        if (!widget) return;

        const animation = widget.addComponent(Animation);
        // TODO: 需要加载实际的动画剪辑资源
        // const clip = animation.createState("cloud");
        // clip.play();

        // 暂时使用 tween 动画替代
        tween(widget)
            .to(1, { scale: new Vec3(1.2, 1.2, 1) })
            .to(1, { scale: new Vec3(1, 1, 1) })
            .start();

        const uiTransform = widget.getComponent(UITransform);
        if (uiTransform) {
            widget.position = new Vec3(
                uiTransform.width / 2,
                uiTransform.height / 2,
                0
            );
        }

        widget.setScale(new Vec3(3, 3, 1));
        widget.active = visible || true;
    }

    /**
     * 添加星星动画
     */
    static addStar(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "star"
        });
    }

    /**
     * 添加清除方块动画
     */
    static addClearCube(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "CubeClear"
        });
    }

    /**
     * 添加火焰动画
     */
    static addFire(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "fire"
        });
    }

    /**
     * 添加光点动画
     */
    static addSpot(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "spot"
        });
    }

    /**
     * 添加彩虹动画
     */
    static addRainbow(params: IAnimationParams): void {
        this.createWithNotEvent({
            ...params,
            AnimationName: "rainbow"
        });
    }

    /**
     * 添加破碎动画
     */
    static addBroken(params: Omit<IAnimationParams, 'visible' | 'scaleF' | 'zOrder'>): void {
        this.create({
            ...params,
            AnimationName: "broken"
        });
    }

    /**
     * 添加点击动画
     */
    static addClick(params: Omit<IAnimationParams, 'visible' | 'scaleF' | 'zOrder'>): void {
        this.create({
            ...params,
            AnimationName: "click"
        });
    }

    /**
     * 添加测试动画
     */
    static addTest(params: Omit<IAnimationParams, 'visible' | 'scaleF' | 'zOrder'>): void {
        this.create({
            ...params,
            AnimationName: "test"
        });
    }

    /**
     * 创建无事件动画
     */
    private static createWithNotEvent(params: IAnimationParams & { AnimationName: string }): void {
        const { widget, index, AnimationName, visible, scaleF, zOrder } = params;
        if (!widget) return;

        // TODO: 需要加载实际的动画剪辑资源
        // const animation = widget.addComponent(Animation);
        // const clip = animation.createState(AnimationName);
        // clip.play();

        // 暂时使用 tween 动画替代
        tween(widget)
            .to(0.5, { scale: new Vec3(scaleF || 1.5, scaleF || 1.5, 1) })
            .to(0.5, { scale: new Vec3(1, 1, 1) })
            .start();

        const uiTransform = widget.getComponent(UITransform);
        if (uiTransform) {
            widget.position = new Vec3(
                uiTransform.width / 2,
                uiTransform.height / 2,
                0
            );
        }

        widget.active = visible || true;
        widget.setScale(new Vec3(scaleF || 1, scaleF || 1, 1));
    }

    /**
     * 创建基础动画
     */
    private static create(params: Omit<IAnimationParams, 'visible' | 'scaleF' | 'zOrder'> & { AnimationName: string }): void {
        const { widget, index, AnimationName } = params;
        if (!widget) return;

        // TODO: 需要加载实际的动画剪辑资源
        // const animation = widget.addComponent(Animation);
        // const clip = animation.createState(AnimationName);
        // clip.play();

        // 暂时使用 tween 动画替代
        tween(widget)
            .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();

        const uiTransform = widget.getComponent(UITransform);
        if (uiTransform) {
            widget.position = new Vec3(
                uiTransform.width / 2,
                uiTransform.height / 2,
                0
            );
        }
    }
}