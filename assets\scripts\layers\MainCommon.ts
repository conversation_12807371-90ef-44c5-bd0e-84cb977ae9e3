import { _decorator, Component, Node, Vec2 } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('MainCommon')
export class MainCommon extends Component {
    @property(Node)
    public hint: Node | null = null;

    @property(Node)
    public willImg1: Node | null = null;

    @property(Node)
    public greenFloor: Node | null = null;

    @property(Node)
    public clockSmall: Node | null = null;

    @property(Node)
    public dottedLine1: Node | null = null;

    @property(Node)
    public bottomViel1: Node | null = null;

    @property(Node)
    public bottomViel2: Node | null = null;

    @property(Node)
    public startCube: Node | null = null;

    @property(Node)
    public lodingCube: Node | null = null;

    @property(Node)
    public arrows: Node | null = null;

    @property(Node)
    public readyGo: Node | null = null;

    @property(Node)
    public backBtn: Node | null = null;

    @property(Node)
    public timeItem: Node | null = null;
}