/**
 * 经典模式游戏逻辑
 * 移植自原JDlogic.js
 * <AUTHOR>
 */

import { ICube } from '../models/ICube';
import { Effects, ChessBoard, CommonEnum } from './EnumHelper';

/**
 * 游戏核心逻辑类
 */
export class GameLogic {
    /**
     * 主要消除逻辑方法
     * @param arr 游戏方块数组
     * @param indexs 已处理的索引组
     */
    static method(arr: (ICube | null)[], indexs: number[][]): void {
        for (let i = 0; i < arr.length; i++) {
            const list: number[] = [];
            this.globalFind(arr, indexs, list, i);
            if (list.length >= 3) {
                indexs.push(list);
                return;
            }
        }
    }

    /**
     * 全局查找相同颜色的方块
     * @param arr 游戏方块数组
     * @param indexs 已处理的索引组
     * @param list 当前处理的索引列表
     * @param i 当前索引
     */
    static globalFind(arr: (ICube | null)[], indexs: number[][], list: number[], i: number): boolean {
        if (arr[i] == null) {
            return false;
        }

        // 跳过可点击的骨头方块
        if (arr[i]!.color == 0) {
            return false;
        }

        // 判断i是否已做过处理
        if (!this.isJump(indexs, i)) { // 判断全局消除列表是否添加过当前索引
            return false;
        }

        if (this.tryAdd(list, i)) { // 判断当前消除列表是否添加当前索引
            return false;
        }

        // 检查左边
        if (i % ChessBoard.ChessWidth != 0) { // 本行第一个，不判断左边
            if (i - 1 >= 0) {
                if (arr[i - 1] != null) {
                    if (arr[i]!.color == arr[i - 1]!.color) {
                        this.runLogic(arr, indexs, list, i - 1);
                    }
                }
            }
        }

        // 检查右边
        if ((i + 1) % ChessBoard.ChessWidth != 0) { // 到本行的最后一个
            if (arr[i + 1] != null && i + 1 <= ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
                if (arr[i]!.color == arr[i + 1]!.color) {
                    this.runLogic(arr, indexs, list, i + 1);
                }
            }
        }

        // 检查上边
        if (i + ChessBoard.ChessWidth < ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
            if (arr[i + ChessBoard.ChessWidth] != null) {
                if (arr[i]!.color == arr[i + ChessBoard.ChessWidth]!.color) {
                    this.runLogic(arr, indexs, list, i + ChessBoard.ChessWidth);
                }
            }
        }

        // 检查下边
        if (i - ChessBoard.ChessWidth >= 0) {
            if (arr[i - ChessBoard.ChessWidth] != null) {
                if (arr[i]!.color == arr[i - ChessBoard.ChessWidth]!.color) {
                    this.runLogic(arr, indexs, list, i - ChessBoard.ChessWidth);
                }
            }
        }

        return true;
    }

    /**
     * 尝试添加索引到列表
     * @param indexs 索引列表
     * @param index 要添加的索引
     * @returns 是否已存在
     */
    static tryAdd(indexs: number[], index: number): boolean {
        let isAdd = false;
        for (let i = 0; i < indexs.length; i++) {
            if (indexs[i] == index) {
                isAdd = true;
                break;
            }
        }
        if (!isAdd) {
            indexs.push(index);
        }
        return isAdd;
    }

    /**
     * 判断是否跳过处理
     * @param indexs 已处理的索引组
     * @param index 要检查的索引
     * @returns 是否可以处理
     */
    static isJump(indexs: number[][], index: number): boolean {
        for (let i = 0; i < indexs.length; i++) {
            for (let j = 0; j < indexs[i].length; j++) {
                if (indexs[i][j] == index) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 运行逻辑处理
     * @param arr 游戏方块数组
     * @param indexs 已处理的索引组
     * @param list 当前处理的索引列表
     * @param i 当前索引
     */
    static runLogic(arr: (ICube | null)[], indexs: number[][], list: number[], i: number): boolean {
        // 判断i是否已做过处理
        if (!this.isJump(indexs, i)) {
            return false;
        }

        if (this.tryAdd(list, i)) {
            return false;
        }

        if (arr[i] == null) {
            return false;
        }

        // 检查左边
        if (i % ChessBoard.ChessWidth != 0) {
            if (i - 1 >= 0) {
                if (arr[i - 1] != null) {
                    if (arr[i]!.color == arr[i - 1]!.color) {
                        this.runLogic(arr, indexs, list, i - 1);
                    }
                }
            }
        }

        // 检查右边
        if ((i + 1) % ChessBoard.ChessWidth != 0) {
            if (arr[i + 1] != null && i + 1 <= ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
                if (arr[i]!.color == arr[i + 1]!.color) {
                    this.runLogic(arr, indexs, list, i + 1);
                }
            }
        }

        // 检查上边
        if (i + ChessBoard.ChessWidth < ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
            if (arr[i + ChessBoard.ChessWidth] != null) {
                if (arr[i]!.color == arr[i + ChessBoard.ChessWidth]!.color) {
                    this.runLogic(arr, indexs, list, i + ChessBoard.ChessWidth);
                }
            }
        }

        // 检查下边
        if (i - ChessBoard.ChessWidth >= 0) {
            if (arr[i - ChessBoard.ChessWidth] != null) {
                if (arr[i]!.color == arr[i - ChessBoard.ChessWidth]!.color) {
                    this.runLogic(arr, indexs, list, i - ChessBoard.ChessWidth);
                }
            }
        }

        return true;
    }

    /**
     * 整理方块（下落逻辑）
     * @param arr 游戏方块数组
     */
    static tidyCube(arr: (ICube | null)[]): boolean {
        for (let i = 0; i < ChessBoard.ChessWidth; i++) {
            // 判断i坐标上面有多少个null
            let count = 0;
            for (let a = i; a < ChessBoard.ChessWidth * ChessBoard.ChessHeight; a += ChessBoard.ChessWidth) {
                if (arr[a] == null) {
                    count++;
                }
            }

            for (let b = 0; b < count; b++) {
                this.runTidy(arr, i);
            }
        }
        return true;
    }

    /**
     * 执行整理逻辑
     * @param arr 游戏方块数组
     * @param i 当前列索引
     */
    static runTidy(arr: (ICube | null)[], i: number): boolean {
        if (arr[i] == null) {
            // 把上面的往下拉一次
            if (i + ChessBoard.ChessWidth < ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
                arr[i] = arr[i + ChessBoard.ChessWidth];
                arr[i + ChessBoard.ChessWidth] = null;
                this.runTidy(arr, i + ChessBoard.ChessWidth);
            }
        } else {
            if (i + ChessBoard.ChessWidth < ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
                this.runTidy(arr, i + ChessBoard.ChessWidth);
            }
        }
        return true;
    }

    /**
     * 添加顶部可点击的骨头方块
     * @param arr 游戏方块数组
     * @param addBoneList 可选的添加列表，用于记录添加的位置
     */
    static addTopBone(arr: (ICube | null)[], addBoneList?: number[]): boolean {
        for (let i = 0; i < ChessBoard.ChessWidth; i++) {
            let isAdd = true;

            for (let a = i; a < ChessBoard.ChessWidth * ChessBoard.ChessHeight; a += ChessBoard.ChessWidth) {
                if (arr[a] == null && isAdd) { // 基本条件
                    if (a < ChessBoard.ChessWidth) {
                        // 添加骨头方块
                        const ca: ICube = {
                            color: 0,
                            eff: Effects.nullEff,
                            statics: 0
                        };
                        arr[a] = ca;
                        if (addBoneList) {
                            addBoneList.push(a);
                        }
                        isAdd = false;
                    } else {
                        if (arr[a - ChessBoard.ChessWidth]!.color == 0) {
                            // 这个添加点的下面一个不是可点击的
                            isAdd = false;
                        } else {
                            // 添加骨头方块
                            const ca: ICube = {
                                color: 0,
                                eff: Effects.nullEff,
                                statics: 0
                            };
                            arr[a] = ca;
                            if (addBoneList) {
                                addBoneList.push(a);
                            }
                            isAdd = false;
                        }
                    }
                }
            }
        }
        return true;
    }
}

/**
 * 方块生成器类
 */
export class CubeLoader {
    /**
     * 生成方块
     * @param cubeArrayLength 方块数组长度
     * @param isHaveBone 是否包含骨头方块
     * @param count 生成数量，默认为1
     * @returns 单个方块或方块数组
     */
    static loadingCube(cubeArrayLength: number, isHaveBone: boolean, count?: number): ICube | ICube[] {
        if (count === undefined) {
            // 生成单个方块
            const cal = this.calcCubeArrayLength(cubeArrayLength);
            let a = Math.floor(Math.random() * cal);

            if (!isHaveBone && a == 0) {
                ++a; // 可选三个不能为0的骨头方块
            }

            const ca: ICube = {
                color: a,
                statics: 0,
                eff: Effects.nullEff
            };
            return ca;
        } else {
            // 生成多个方块
            const cal = this.calcCubeArrayLength(cubeArrayLength);

            if (count < 1) {
                count = 1;
            }

            const arr: ICube[] = [];
            for (let i = 0; i < count; i++) {
                let a = Math.floor(Math.random() * cal);
                if (!isHaveBone && a == 0) {
                    ++a; // 可选三个不能为0的骨头方块
                }

                const ca: ICube = {
                    color: a,
                    statics: 0,
                    eff: Effects.sixEff
                };
                arr.push(ca);
            }

            return arr;
        }
    }

    /**
     * 计算方块数组长度
     * @param cubeArrayLength 输入的长度
     * @returns 校验后的长度
     */
    static calcCubeArrayLength(cubeArrayLength: number): number {
        if (isNaN(cubeArrayLength) || cubeArrayLength < CommonEnum.minCubeArrayLength) {
            cubeArrayLength = CommonEnum.minCubeArrayLength;
        }
        if (cubeArrayLength > CommonEnum.maxCubeArrayLength) {
            cubeArrayLength = CommonEnum.maxCubeArrayLength;
        }
        return cubeArrayLength;
    }
}
