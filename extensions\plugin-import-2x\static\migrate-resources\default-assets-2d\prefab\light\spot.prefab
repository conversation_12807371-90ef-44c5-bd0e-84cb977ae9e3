[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "light", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 1, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "_is3DNode": true, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}}, {"__type__": "cc.Light", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_type": 2, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_intensity": 1, "_range": 1000, "_spotAngle": 60, "_spotExp": 1, "_shadowType": 0, "_shadowResolution": 1024, "_shadowDarkness": 0.5, "_shadowMinDepth": 1, "_shadowMaxDepth": 1000, "_shadowDepthScale": 250, "_shadowFrustumSize": 50, "_shadowBias": 0.0005, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f5331fd2-bf42-4ee3-a3fd-3e1657600eff"}, "fileId": "33zOkteexCqamZ4PXuwJaW", "sync": false}]