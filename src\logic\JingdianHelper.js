/**
 * Created by <PERSON><PERSON> on 15/8/23.
 */

//经典相关助手类
var JingdianHelper = {
    //加载三个色块
    willImgHelper: function (root, willCube) {
        //设置willImg的色块颜色
        var will1 = ccui.helper.seekWidgetByTag(root, EnumHelper.MainCommonEnum.willImg1);
        will1.loadTexture(RES.GET_DOG(willCube[0].color), ccui.Widget.PLIST_TEXTURE);
        var will2 = ccui.helper.seekWidgetByTag(root, EnumHelper.MainCommonEnum.willImg2);
        will2.loadTexture(RES.GET_DOG(willCube[1].color), ccui.Widget.PLIST_TEXTURE);
        var will3 = ccui.helper.seekWidgetByTag(root, EnumHelper.MainCommonEnum.willImg3);
        will3.loadTexture(RES.GET_DOG(willCube[2].color), ccui.Widget.PLIST_TEXTURE);
    },
    //加载矩阵方法(用于前端显示)
    matrixHelper: function (jingdian) {
        var root = jingdian._root;
        var bgLayout = jingdian._bgLayout;
        var arr = jingdian._arr;

        //获取矩阵第一个位置
        var startCube = ccui.helper.seekWidgetByTag(root, EnumHelper.MainCommonEnum.startPoint);

        //var ns = res.Dog0_png;
        var ns = RES.GET_DOG(0);

        for (var i = 0; i < arr.length; i++) {
            //清掉之前的色块
            var oldImg = root.getChildByTag(900 + i);
            if (oldImg != null) {
                root.removeChildByTag(900 + i);
            }
            if (arr[i] != null) {

                //ns = "res/dog/dog" + arr[i].color + ".png";
                ns = RES.GET_DOG(arr[i].color);
                var imageView = startCube.clone();
                imageView.loadTexture(ns, ccui.Widget.PLIST_TEXTURE);
                imageView.setVisible(true);

                var position = startCube.getPosition();
                /*
                 第一行起始位置 = y*0 + startCube.position.y
                 第二行起始位置 = y*1 + startCube.position.y
                 第三行起始位置 = y*2 + startCube.position.y
                 */
                var x = position.x + i % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
                var y = position.y + i / EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().height * startCube.getScale();

                imageView.setPosition(x, y);
                imageView.setScale(startCube.getScale());
                imageView.setTag(900 + i);
                if (arr[i].color == 0)//点击区域算法
                {
                    imageView.setTouchEnabled(true);
                    imageView.addTouchEventListener(jingdian.clicks, jingdian);
                }
                if (arr[i].eff == EnumHelper.Effects.fourEff) {
                    AnimationHelper.addFire(jingdian, imageView, i, true, 1, 10);
                }
                if (arr[i].eff == EnumHelper.Effects.fiveEff) {
                    AnimationHelper.addSpot(jingdian, imageView, i, true, 1, 10);
                }
                if (arr[i].eff >= EnumHelper.Effects.sixEff) {
                    AnimationHelper.addRainbow(jingdian, imageView, i, true, 1, 10);
                }

                root.addChild(imageView, 5);
            }
        }
    },
    //加载顶部可点击色块
    topBoneCube: function (jingdian) {
        var root = jingdian._root;
        var startCube = ccui.helper.seekWidgetByTag(root, EnumHelper.MainCommonEnum.startPoint);
        var position = startCube.getPosition();

        var boneList = [];
        Logic.addTopBone(jingdian._arr, boneList);
        for (var i = 0; i < boneList.length; i++) {
            var x = position.x + boneList[i] % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
            var y = position.y + boneList[i] / EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().height * startCube.getScale();

            var img = new ccui.ImageView(RES.GET_DOG(0), ccui.Widget.PLIST_TEXTURE);
            img.setPosition(x, y);
            img.setScale(startCube.getScale());
            img.setTag(900 + boneList[i]);
            img.setTouchEnabled(true);
            img.addTouchEventListener(jingdian.clicks, jingdian);
            //AnimationHelper.addFire(jingdian, img, i, true);
            jingdian._root.addChild(img, 5);
        }
    },
    //用于Eff.five时,横竖位置为空补位用
    addOneCube: function (i, jingdian, cubeName) {
        //获取矩阵第一个位置
        var startCube = ccui.helper.seekWidgetByTag(jingdian._root, EnumHelper.MainCommonEnum.startPoint);
        var position = startCube.getPosition();
        var x = position.x + i % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
        var y = position.y + i / EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().height * startCube.getScale();

        var imageView = new ccui.ImageView(cubeName, ccui.Widget.PLIST_TEXTURE);
        imageView.setPosition(x, y);
        imageView.setScale(startCube.getScale());
        imageView.setTag(900 + i);
        jingdian._root.addChild(imageView);
    },
    //添加白色边框和星星效果,
    addChildCube: function (widget, cubeName) {
        if (widget != null) {
            var imageview = new ccui.ImageView(cubeName, ccui.Widget.PLIST_TEXTURE);
            imageview.setPosition(widget.getContentSize().width / 2, widget.getContentSize().height / 2);
            widget.addChild(imageview);
        }
    },
    //添加白色消除狂 和眨眼Action
    addWhiteCube: function (widget) {
        var actImg = AboutHelper.addChildCube(widget, RES.GET_DOG(10));
        actImg.setOpacity(0);
        var suq = cc.sequence(cc.fadeTo(0.1, 255));
        actImg.runAction(suq);
    },
    //加动画
    addAnimation: function (jingdian) {
        var root = jingdian._root;
        var bgLayout = jingdian._bgLayout;
        var indexs = jingdian._indexs;
        if (indexs.length < 1) {
            return;
        }
        var efflist = [];

        //TODO:增加combo数量
        jingdian._currentCombo++;
        //调用hintHelper,用Action向上移动
        if (jingdian._currentCombo > 1) {
            HintHelper.setCombo(jingdian);
        }
        //给要消除的色块增加动画
        var list = indexs[0];
        if (list.length < 3) {
            return;
        }

        for (var i = 0; i < list.length; i++) {
            var imgview = ccui.helper.seekWidgetByTag(root, 900 + list[i]);
            if (imgview != null) {
                //帧动画特效
                AnimationHelper.addTest(jingdian, imgview, list[i]);
                //白色边框
                this.addChildCube(imgview, RES.GET_DOG(10));

                //判断是否是三个以上的,聚拢
                if (list.length > 3) {
                    var startimgview = ccui.helper.seekWidgetByTag(root, 900 + list[0]);
                    if (startimgview) {

                        var act = cc.moveTo(0.1, startimgview.getPosition());
                        imgview.runAction(act);
                    }
                }

                //判断特效影响色块,直接变成黑色
                var arr = jingdian._arr;

                if (arr[list[i]].eff == EnumHelper.Effects.fourEff) {
                    /** 四种效果 **/
                    //下面的全部变成黑色,并新建vector,添加到indexs中

                    var blackDog = [];
                    var pagesize = 0;
                    while (list[i] - pagesize * EnumHelper.ChessBoard.ChessWidth >= 0) {
                        imgview = ccui.helper.seekWidgetByTag(root, 900 + list[i] - pagesize * EnumHelper.ChessBoard.ChessWidth);
                        if (imgview != null) {
                            //var str = res.Dog8_png;
                            imgview.loadTexture(RES.GET_DOG(8), ccui.Widget.PLIST_TEXTURE);
                        }

                        arr[list[i] - pagesize * EnumHelper.ChessBoard.ChessWidth].color = 8;
                        blackDog.push(list[i] - pagesize * EnumHelper.ChessBoard.ChessWidth);
                        pagesize++;
                    }
                    efflist.push(blackDog);
                    /** end **/
                }
                if (arr[list[i]].eff == EnumHelper.Effects.fiveEff) {
                    /** 五种效果 **/
                    //横竖全部编程白色,并新建vector,添加到indexs中
                    var whiteDog = [];
                    pagesize = 0;
                    //清除竖列
                    var column = list[i] % EnumHelper.ChessBoard.ChessWidth;
                    while (column + pagesize * EnumHelper.ChessBoard.ChessWidth < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessHeight) {
                        imgview = ccui.helper.seekWidgetByTag(root, 900 + column + pagesize * EnumHelper.ChessBoard.ChessWidth);
                        if (imgview != null) {
                            imgview.loadTexture(RES.GET_DOG(9), ccui.Widget.PLIST_TEXTURE);
                            if (arr[column + pagesize * EnumHelper.ChessBoard.ChessWidth] != null) {
                                arr[column + pagesize * EnumHelper.ChessBoard.ChessWidth].color = 9;
                            }
                            whiteDog.push(column + pagesize * EnumHelper.ChessBoard.ChessWidth);
                        }
                        else {
                            this.addOneCube(column + pagesize * EnumHelper.ChessBoard.ChessWidth, jingdian, RES.GET_DOG(9));
                        }
                        pagesize++;
                    }

                    pagesize = 0;
                    //清除横排
                    var row = list[i] / EnumHelper.ChessBoard.ChessWidth;
                    for (var a = list[i] / EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessWidth; a < list[i] / EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessWidth + 7; a++) {
                        imgview = ccui.helper.seekWidgetByTag(root, 900 + a);
                        if (imgview != null) {
                            imgview.loadTexture(RES.GET_DOG(9), ccui.Widget.PLIST_TEXTURE);
                            arr[a].color = 9;
                            whiteDog.push(a);
                        }
                        else {
                            this.addOneCube(a, jingdian, RES.GET_DOG(9));
                        }
                    }
                    efflist.push(whiteDog);
                    /** end **/
                }
                if (arr[list[i]].eff >= EnumHelper.Effects.sixEff) {
                    //所有与arr[list[i]].color相同的颜色加上动画, 建立vector 添加到indexs中.
                    /*加上变白效果,加上星星效果,加上例子动画, 加上moveTo的Flow效果*/
                    var lists = this.findColorCube(jingdian, list[i]);
                    this.changeColor(jingdian, lists, RES.GET_DOG(9), list[i]);
                    efflist.push(lists);
                }
            }
        }
        for (i = 0; i < efflist.length; i++) {
            jingdian._indexs.push(efflist[i]);
        }
    },
    //寻找与index位置相同color的cube
    findColorCube: function (jingdian, index) {
        var arr = jingdian._arr;
        var list = [];
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] != null && arr[i].color == arr[index].color) {
                list.push(i);
            }
        }
        return list;
    },
    //把list中的cube改为changeColorIndex位置的color,把前端的色块改成changeColorName
    changeColor: function (jingdian, list, changeColorName, changeColorIndex) {
        for (var i = 0; i < list.length; i++) {
            //更变颜色. 修改arr
            var imgview = ccui.helper.seekWidgetByTag(jingdian._root, 900 + list[i]);
            if (imgview != null) {
                imgview.loadTexture(changeColorName, ccui.Widget.PLIST_TEXTURE);
            }
            if (jingdian._arr[list[i]] != null) {
                jingdian._arr[list[i]].color = changeColorIndex;
            }
        }
    },
    //清除list包含的色块,并附加效果
    clearCubeHelper: function (jingdian, list) {
        var arr = jingdian._arr;

        var isEff = false;//判断当先消除的这个list 是不是受特效影响的列表(受特效消除的list内的色块颜色不一定相同.)
        for (var i = 0; i < list.length; i++) {
            if (arr[list[i]] != null && arr[list[0]] != null) {
                // 颜色不同,或者都骨头
                if (arr[list[0]].color != arr[list[i]].color || arr[list[0]].color == EnumHelper.Bone)
                    isEff = true;
            }
            else {
                isEff = true;
            }
        }

        if (isEff) {
            for (i = 0; i < list.length; i++) {
                arr[list[i]] = null;
            }
        }
        else {
            for (i = 0; i < list.length; i++) {
                if (i == 0 && list.length > 3) {
                    if (list.length == EnumHelper.Effects.fourEff) {
                        //四种的效果
                        arr[list[i]].eff = EnumHelper.Effects.fourEff;
                    }
                    if (list.length == EnumHelper.Effects.fiveEff) {
                        //五种的效果
                        arr[list[i]].eff = EnumHelper.Effects.fiveEff;
                    }
                    if (list.length >= EnumHelper.Effects.sixEff) {
                        //六种以上的效果
                        arr[list[i]].eff = EnumHelper.Effects.sixEff;

                    }
                }
                else {
                    arr[list[i]] = null;
                }
            }
        }
    },
    //计算分数
    calcCount: function (jingdian, fenshu, index) {
        var bgLayout = jingdian._bgLayout;
        var txt = ccui.helper.seekWidgetByTag(bgLayout, EnumHelper.jingdianEnum.fenshuLabel);
        if (txt != null) {
            var str = txt.getString();
            var num = parseInt(str);
            num += fenshu;
            txt.setString(num);

            //设置数字标签提示
            var hintLB = ccui.helper.seekWidgetByTag(jingdian._hint, EnumHelper.MainCommonEnum.hintLabelAtlas);
            var txtHint = hintLB.clone();
            txtHint.setString(fenshu);
            txtHint.setContentSize(txtHint.getVirtualRendererSize());

            var startCube = ccui.helper.seekWidgetByTag(jingdian._root, EnumHelper.MainCommonEnum.startPoint);
            var position = startCube.getPosition();
            var x = position.x + index % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
            var y = position.y + index / EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().height * startCube.getScale();

            txtHint.setPosition(x, y);

            var act = cc.sequence(
                cc.moveTo(0.5, cc.p(x, y + 20)),
                cc.callFunc(jingdian.calcMoveOver, jingdian)
            );
            txtHint.runAction(act);
            jingdian._playLayout.addChild(txtHint);

            //色块长度成长规则: 最小长度 + 等级 / 2
            jingdian._cubeLength = EnumHelper.CommonEnum.minCubeArrayLength + parseInt(parseInt(num / EnumHelper.CommonEnum.levelRemainder) / 2);
            //等级规则
            var level = ccui.helper.seekWidgetByTag(bgLayout, EnumHelper.jingdianEnum.levelLabelAtlas);
            if (level != null) {
                level.setString(parseInt(num / EnumHelper.CommonEnum.levelRemainder));
                level.setContentSize(level.getVirtualRendererSize());
            }
        }
    },
    calcMoveOver: function () {

    },
    getLevelAndScore: function (jingdian, level, score) {
        var txt = ccui.helper.seekWidgetByTag(jingdian._bgLayout, EnumHelper.jingdianEnum.fenshuLabel);
        if (txt != null) {
            score = txt.getString();
        }
        var levelLable = ccui.helper.seekWidgetByTag(jingdian._bgLayout, EnumHelper.jingdianEnum.levelLabelAtlas);
        if (levelLable != null) {
            level = levelLable.getString();
        }
    },
    setGameOverLayout: function (jingdian) {
        //TODO: 奖杯 和 news图标
        var level = "";
        var score = "";
        //设置结算面板分数
        var txt = ccui.helper.seekWidgetByTag(jingdian._bgLayout, EnumHelper.jingdianEnum.fenshuLabel);
        if (txt != null) {
            score = txt.getString();
        }
        var levelLable = ccui.helper.seekWidgetByTag(jingdian._bgLayout, EnumHelper.jingdianEnum.levelLabelAtlas);
        if (levelLable != null) {
            level = levelLable.getString();
        }
        //this.getLevelAndScore(jingdian, level, score);
        var maxScore = 0;
        var maxLevel = 0;

        //var fileData = XMLHelper.loadXML("scriptureRankList.xml");
        //XMLHelper.saveXML("scriptureRankList.xml", fileData);

        //TODO: 最高记录的获取
        //XMLHelper.getJingdianRankList(maxScore, maxLevel, "scriptureRankList.xml");

        //最高分数
        var maxScoreLabel = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.mexScore_gameOver);
        if (maxScoreLabel != null)
        {
            maxScoreLabel.setString(maxScore);
        }
        //最高等级
        var maxLevelLable = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.maxLevel_Text_gameOver);
        if (maxLevelLable != null)
        {
            maxLevelLable.setString(maxLevel);
        }

        //设置当前分数
        var currentScoreLabel = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.score_gameOver);
        if (currentScoreLabel != null)
        {
            currentScoreLabel.setString(score);
        }
        //关卡, level
        var levelLabel = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.level_gameOver);
        if (levelLabel != null)
        {
            levelLabel.setString(level);
        }
        //最高连击
        var maxCumbo = jingdian._maxCombo;
        var maxCumboLabel = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.combo_gameOver);
        if (maxCumboLabel != null)
        {
            maxCumboLabel.setString(maxCumbo);
        }

        //判断maxScore 和score 的大小,  确定news的图标要不要
        var newImg = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.newImg_gameOver);
        if (newImg != null)
        {
            newImg.setVisible(false);
        }
        var intMaxScore = parseInt(maxScore);
        var intScore = parseInt(score);
        if (intMaxScore < intScore)
        {
            newImg.setVisible(true);
        }

        //设置背景的旋转
        var bgcup = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.cupbg_gameOver);
        if (bgcup != null)
        {
            var bgAction = cc.rotateBy(10, 360);
            bgcup.runAction(bgAction);
        }

        //获取奖杯图标
        var cupLevel = parseInt(intScore / EnumHelper.CommonEnum.cupRemainder);
        if (cupLevel > 8)
        {
            cupLevel = 8;
        }
        //var cupName = "res/cup/" + cupLevel + ".png";
        var cupName = RES.GET_CUP(cupLevel);
        var cupImg = ccui.helper.seekWidgetByTag(jingdian._gameOver, EnumHelper.gameOverEnum.goldCup_gameOver);
        if (cupImg != null)
        {
            cupImg.loadTexture(cupName, ccui.Widget.PLIST_TEXTURE);
        }

        //TODO: 写入排行榜
        /*
         logic : 把本条相关的信息传递进去
         */
        //RankListModel* rlm = new RankListModel();
        //rlm.cup = cupName;
        //rlm.level = level;
        //rlm.score = Utility.parseInt(score);// score;
        //
        //time_t tmNow = time(null);
        //var times = localtime(&tmNow);//获取到的时间 , 年份加1900,  月份加1
        //var str = "%d-%d-%d", times.tm_year + 1900, times.tm_mon + 1, times.tm_mday);
        //rlm.date = str;
        //var currentRank = 0;
        //XMLHelper.tryAddRank(rlm, "scriptureRankList.xml", currentRank);
    },
    isTryAdd: function (isok, jingdian) {
        for (var i = EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight; i > EnumHelper.ChessBoard.ChessWidth * (EnumHelper.ChessBoard.ChessHeight - 1); i--) {
            if (jingdian._arr[i - 1] != null) {
                //顶部没有有部位空的色块,停止计时器,并退出方法
                isok = true;
                return;
            }
        }
        isok = false;
    },

    /** bottom method **/
    //从bottomCube中填充matrixCube
    addFromArr: function (jingdian) {
        var arr = jingdian._arr;
        var bottomCube = jingdian._bottomCube;
        for (var i = EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight; i > 0; i--) {
            if (arr[i - 1] != null) {
                arr[i - 1 + EnumHelper.ChessBoard.ChessWidth] = arr[i - 1];
                arr[i - 1] = null;
            }
        }
        //吧bottomCube的几个添加dao arr的开始
        for (i = 0; i < bottomCube.length; i++) {
            arr[i] = bottomCube[i];
        }
    },
    //params=3 : 添加bottom到root,仅仅加载最后一个   params=2 : 循环加载全部的bottom
    addLayoutFromBottom: function (jingdian, bottomCube, root) {
        var i;
        var lodingCube;
        var ns;
        var imageView;
        var position;
        var height;
        var x;
        var y;
        var act;

        if (arguments.length = 3) {
            if (bottomCube.length < 1) {
                return;
            }
            i = bottomCube.length - 1;
            //获取加载行第一个位置
            lodingCube = ccui.helper.seekWidgetByTag(root, EnumHelper.loadingPoint);
            if (lodingCube != null) {
                lodingCube.setVisible(false);
            }

            //ns = "res/dog/dog" + bottomCube[i].color + ".png";
            ns = RES.GET_DOG(bottomCube[i].color);
            imageView = new ccui.ImageView(ns, ccui.Widget.PLIST_TEXTURE);

            position = lodingCube.getPosition();
            /*
             第一行起始位置 = y*0 + startCube.position.y
             第二行起始位置 = y*1 + startCube.position.y
             第三行起始位置 = y*2 + startCube.position.y
             */
            height = lodingCube.getContentSize().height * lodingCube.getScale();

            x = position.x + i % EnumHelper.ChessBoard.ChessWidth * lodingCube.getContentSize().width * lodingCube.getScale();
            y = position.y; // +i / EnumHelper.ChessBoard.ChessWidth * lodingCube.getContentSize().height * lodingCube.getScale();

            imageView.setPosition(x, y - height / 2);

            //加入action
            act = cc.moveTo(0.1, cc.p(x, y));
            imageView.runAction(act);

            if (bottomCube[i].color == 0)//点击区域算法
            {
                imageView.setTouchEnabled(true);
                imageView.addTouchEventListener(jingdian.clicks, jingdian);
            }

            imageView.setScale(lodingCube.getScale());
            imageView.setTag(800 + i);
            imageView.setLocalZOrder(lodingCube.getLocalZOrder());
            root.addChild(imageView, 3);
        } else if (arguments.length = 2) {
            if (bottomCube.length < 1) {
                return;
            }
            for (i = 0; i < bottomCube.length; i++) {
                lodingCube = ccui.helper.seekWidgetByTag(jingdian._root, EnumHelper.MainCommonEnum.loadingPoint);
                if (lodingCube != null) {
                    lodingCube.setVisible(false);
                }
                //ns = "res/dog/dog" + bottomCube[i].color + ".png";
                ns = RES.GET_DOG(bottomCube[i].color);
                imageView = new ccui.ImageView(ns, ccui.Widget.PLIST_TEXTURE);
                position = lodingCube.getPosition();
                height = lodingCube.getContentSize().height * lodingCube.getScale();

                x = position.x + i % EnumHelper.ChessBoard.ChessWidth * lodingCube.getContentSize().width * lodingCube.getScale();
                y = position.y; // +i / EnumHelper.ChessBoard.ChessWidth * lodingCube.getContentSize().height * lodingCube.getScale();

                imageView.setPosition(x, y - height / 2);

                //加入action
                act = cc.moveTo(0.1, cc.p(x, y));
                imageView.runAction(act);

                imageView.setScale(lodingCube.getScale());
                imageView.setTag(800 + i);
                imageView.setLocalZOrder(lodingCube.getLocalZOrder());
                jingdian._root.addChild(imageView, 3);
            }
        }
    },
    //清除root中的bottom
    clearLayoutFromBottom: function (jingdian, root, bottomCube) {
        for (var i = 0; i < EnumHelper.ChessBoard.ChessWidth; i++) {
            var oldImg = root.getChildByTag(800 + i);
            if (oldImg != null) {
                root.removeChildByTag(800 + i);
            }
        }
        bottomCube = [];
    }
    /** end **/
};