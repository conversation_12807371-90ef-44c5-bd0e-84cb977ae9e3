import { _decorator, <PERSON><PERSON>, Component, director, Node, UITransform, Vec3 } from 'cc';
import { MainCommon } from './MainCommon';
import { JingdianBg } from './JingdianBg';
import { PauseLayout } from './PauseLayout';
import { CommonEnum } from '../helpers/EnumHelper';

const { ccclass, property } = _decorator;

/**
 * 经典模式游戏场景
 */
@ccclass('JingdianScene')
export class JingdianScene extends Component {

    @property(Node)
    private mainCommon: Node | null = null;

    @property(Node)
    private byLayout: Node | null = null;

    @property(Node)
    private pauseLayout: Node | null = null;

    private _root: MainCommon;
    private _willCubeVec2: Vec3 = null;

    private _bg: JingdianBg;
    private _pause: PauseLayout;
    private _playLayout: Node | null = null;
    private _mu: Node | null = null;                   // 遮罩透明幕

    // 游戏状态
    private _goonGame: boolean = false;                // 是否为继续游戏
    private _arr: (ICube | null)[] = [];              // 矩阵列表
    private _willCube: ICube[] = [];                   // 初始化预选择色块列表
    private _bottomCube: ICube[] = [];                 // 底部色块列表
    private _cubeLength: number = 0;                   // 初始化可选色块的长度
    private _line: number = 3;                         // 初始化加载行数
    private _indexs: number[][] = [];                  // 消除列表容器
    private _second: number = 0;                       // 秒数
    private _finishAnimationCount: number = 0;         // 已完成动画的个数
    private _mainScheduleSpeed: number = 0.1;          // 底部初始加载速度
    private _willCubeVec2: Vec2 = new Vec2();          // willCube Vec2 第一个加载色块的位置
    private _maxCombo: number = 0;                     // 最高连击
    private _currentCombo: number = 0;                 // 当前连击
    private _comboSecond: number = 0;
    private _clearEffSize: number[] = [];              // 特效大小列表

    onLoad() {
        this.initMainUI();
        this.initBgLayer();
        this.initPauseUI();

        this.initializeGame();

        // 创建游戏层
        this._playLayout = new Node('PlayLayout');
        const playLayoutTransform = this._playLayout.addComponent(UITransform);
        playLayoutTransform.setContentSize(1920, 1080); // 设置合适的尺寸
        this.node.addChild(this._playLayout);
        this._playLayout.setSiblingIndex(99999);

        // 设置遮罩
        this.setupMask();
    }

    private initMainUI() {
        this._root = this.mainCommon.getComponent(MainCommon);
        this._willCubeVec2 = this._root.willImg1.position.clone();

        this._root.greenFloor.setSiblingIndex(999999);

        //一格时间图标
        this._root.clockSmall.active = false;

        //一格线
        this._root.dottedLine1.active = false;

        //木板
        this._root.bottomViel1.active = false;
        //小草
        this._root.bottomViel2.getComponent(Button).interactable = true;

        //获取矩阵第一个位置
        this._root.startCube.active = false;

        //获取加载行第一个位置
        this._root.lodingCube.active = false;

        //向上提示的箭头
        this._root.arrows.setSiblingIndex(10);
        this._root.arrows.active = false;

        this._root.readyGo.active = false;

        // this._root.hint.setParent(this);
        this._root.hint.setSiblingIndex(10000);
        this._root.hint.active = false;

        // 暂停按钮
        this._root.backBtn.on('click', () => {
            //TODO.暂停所有schedule
            this.pauseLayout.active = true;
        }, this);

        // 时间道具
        this._root.timeItem.on('click', () => {
            this.resetLoadingBar();
        }, this);
    }

    private initBgLayer() {
        this._bg = this.byLayout.getComponent(JingdianBg);
        this._bg.txt.string = "0";
        this._bg.level.string = "0";
    }

    private initPauseUI() {
        this._pause = this.pauseLayout.getComponent(PauseLayout);
        this._pause.btnSaveEsc.on('click', () => {
            //TODO.保存到xml
            director.loadScene('launch');
        }, this);

        this._pause.btnPauseNew.on('click', () => {
            director.loadScene('jingdian');
        }, this);

        this._pause.btnPauseContinue.on('click', () => {
            this.pauseLayout.active = false;
        }, this);
    }

    /**
     * 初始化游戏数据
     */
    private initializeGame(): void {
        this._goonGame = false;
        this._mainScheduleSpeed = 0.1;
        this._line = 3;
        this._cubeLength = CommonEnum.minCubeArrayLength;

        // 初始化数组
        this._arr = [];
        this._bottomCube = [];
        this._second = CommonEnum.jingdianSecond;
        this._maxCombo = 0;
        this._currentCombo = 0;

        // 生成初始色块
        this._willCube = this.generateInitialCubes(this._cubeLength);
    }

    /**
     * 设置遮罩
     */
    private setupMask(): void {
        this._mu = new Node('Mask');
        const muTransform = this._mu.addComponent(UITransform);
        muTransform.setContentSize(1920, 1080);
        this._mu.setPosition(0, 0);
        this._mu.active = false;
        this.node.addChild(this._mu);
        this._mu.setSiblingIndex(3);
    }

    /**
     * 开始游戏
     */
    private startGame(): void {
        // 设置willImg的色块颜色
        this.updateWillCubeDisplay();

        // 开始主循环
        this.schedule(this.mainSchedule, this._mainScheduleSpeed);

        // 播放准备动画
        this.playReadyGoAnimation();
    }

    // ==================== 游戏逻辑方法 ====================

    /**
     * 更新willCube显示
     */
    private updateWillCubeDisplay(): void {
        if (!this._root) return;

        const will1 = this.findNodeByTag(this._root, MainCommon.Elements.willImg1);
        const will2 = this.findNodeByTag(this._root, MainCommon.Elements.willImg2);
        const will3 = this.findNodeByTag(this._root, MainCommon.Elements.willImg3);

        if (will1 && this._willCube[0]) {
            this.setCubeTexture(will1, this._willCube[0].color);
        }
        if (will2 && this._willCube[1]) {
            this.setCubeTexture(will2, this._willCube[1].color);
        }
        if (will3 && this._willCube[2]) {
            this.setCubeTexture(will3, this._willCube[2].color);
        }
    }

    /**
     * 设置方块纹理
     */
    private setCubeTexture(node: Node, color: number | null): void {
        if (color === null) return;

        const sprite = node.getComponent(Sprite);
        if (sprite) {
            // TODO: 根据颜色设置对应的纹理
            // sprite.spriteFrame = this.getCubeTexture(color);
        }
    }

    /**
     * 主游戏循环
     */
    private mainSchedule(dt: number): void {
        const d = Math.floor(dt);
        const isrun = this.judgeMove(this._line);

        if (isrun) {
            if (d == 0) {
                // 初次加载完成, GO动画
                this.playGoAnimation();

                this.unschedule(this.mainSchedule);
                this.addBoneCube();
                this._mainScheduleSpeed = 1;
                this.schedule(this.mainSchedule, this._mainScheduleSpeed);
                this.schedule(this.loadingSchedule, this._mainScheduleSpeed);
                return;
            }
        }

        if (this._bottomCube.length >= ChessBoard.ChessWidth) {
            const isrun2 = this.judgeMove(ChessBoard.ChessHeight);
            if (!isrun2) {
                this.moveCube();
            }
        } else {
            const cube = this.generateSingleCube(this._cubeLength, true);
            this._bottomCube.push(cube);
            this.createCube(cube);
        }
    }

    /**
     * 播放准备动画
     */
    private playReadyGoAnimation(): void {
        // TODO: 实现准备动画
        console.log("播放准备动画");
    }

    /**
     * 播放GO动画
     */
    private playGoAnimation(): void {
        // TODO: 实现GO动画
        console.log("播放GO动画");
    }

    /**
     * 重置加载条
     */
    private resetLoadingBar(): void {
        if (!this._root) return;

        const secondImg = this.findNodeByTag(this._root, MainCommon.Elements.secondImg);
        const loadingBar = this.findNodeByTag(this._root, MainCommon.Elements.timerLoadingBar);
        const secondTxt = this.findNodeByTag(this._root, MainCommon.Elements.secondTxt);

        this._second = 60; // 重置为初始时间

        if (secondTxt) {
            const label = secondTxt.getComponent(Label);
            if (label) {
                label.string = this._second.toString();
            }
        }

        if (loadingBar) {
            const progressBar = loadingBar.getComponent(ProgressBar);
            if (progressBar) {
                progressBar.progress = 1.0;
            }
        }

        if (secondImg && loadingBar) {
            // 重置秒数图标位置
            const loadingBarTransform = loadingBar.getComponent(UITransform);
            if (loadingBarTransform) {
                const newX = loadingBar.position.x + loadingBarTransform.contentSize.width * loadingBar.scale.x;
                secondImg.setPosition(newX, secondImg.position.y);
            }
        }
    }

    /**
     * 生成单个方块
     */
    private generateSingleCube(cubeLength: number, isRandom: boolean): ICube {
        return {
            color: isRandom ? Math.floor(Math.random() * 4) + 1 : 0,
            statics: null,
            eff: 0
        };
    }

    /**
     * 判断是否可以移动
     */
    private judgeMove(line: number): boolean {
        if (line < 1) {
            return true;
        }

        for (let i = (line - 1) * ChessBoard.ChessWidth; i < line * ChessBoard.ChessWidth; i++) {
            if (this._arr[i] != null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 添加骨头方块
     */
    private addBoneCube(): void {
        if (!this._root) return;

        const startCube = this.findNodeByTag(this._root, MainCommon.Elements.startPoint);
        if (!startCube) return;

        const position = startCube.position;
        const boneList: number[] = [];

        // 添加顶部骨头方块的逻辑
        this.addTopBone(this._arr, boneList);

        for (let i = 0; i < boneList.length; i++) {
            const startCubeTransform = startCube.getComponent(UITransform);
            if (!startCubeTransform) continue;

            const x = position.x + (boneList[i] % ChessBoard.ChessWidth) * startCubeTransform.contentSize.width * startCube.scale.x;
            const y = position.y + Math.floor(boneList[i] / ChessBoard.ChessWidth) * startCubeTransform.contentSize.height * startCube.scale.y;

            // 创建新的方块节点
            const cubeNode = new Node(`Cube_${boneList[i]}`);
            const sprite = cubeNode.addComponent(Sprite);
            const transform = cubeNode.addComponent(UITransform);

            // 设置位置和缩放
            cubeNode.setPosition(x, y);
            cubeNode.setScale(startCube.scale.x * 0.8, startCube.scale.y * 0.8);

            // 设置标签
            NodeHelper.setNodeTag(cubeNode, 900 + boneList[i]);

            // 添加到根节点
            this._root.addChild(cubeNode);
            cubeNode.setSiblingIndex(5);

            // 缩放动画
            tween(cubeNode)
                .to(0.1, { scale: startCube.scale })
                .start();

            // 添加点击事件
            const button = cubeNode.addComponent(Button);
            button.node.on(Button.EventType.CLICK, () => this.onCubeClick(cubeNode), this);
        }
    }

    /**
     * 添加顶部骨头方块的逻辑
     */
    private addTopBone(arr: (ICube | null)[], boneList: number[]): void {
        // 简化的添加逻辑，实际应该根据游戏规则来实现
        for (let i = 0; i < ChessBoard.ChessWidth; i++) {
            if (arr[i] === null) {
                boneList.push(i);
                arr[i] = {
                    color: 0, // 骨头方块
                    statics: null,
                    eff: 0
                };
            }
        }
    }

    /**
     * 方块点击事件
     */
    private onCubeClick(cubeNode: Node): void {
        if (!this._willCube[0]) return;

        // 设置方块颜色
        this.setCubeTexture(cubeNode, this._willCube[0].color);

        // 禁用点击
        const button = cubeNode.getComponent(Button);
        if (button) {
            button.interactable = false;
        }

        // 更新矩阵数据
        const tag = NodeHelper.getNodeTag(cubeNode);
        const index = tag - 900;
        if (index >= 0 && index < this._arr.length) {
            this._arr[index] = {
                color: this._willCube[0].color,
                statics: null,
                eff: 0
            };
        }

        // 移动willCube
        this.moveWillCube();

        // 触发移动回调
        this.moveCubeActionCallBack();
    }

    /**
     * 移动willCube
     */
    private moveWillCube(): void {
        // 移除第一个，添加新的
        this._willCube.shift();
        this._willCube.push(this.generateSingleCube(this._cubeLength, false));
        this.updateWillCubeDisplay();
    }

    /**
     * 创建方块
     */
    private createCube(cube: ICube): void {
        if (!this._root) return;

        const loadingCube = this.findNodeByTag(this._root, MainCommon.Elements.loadingPoint);
        if (!loadingCube) return;

        const position = loadingCube.position;
        const loadingTransform = loadingCube.getComponent(UITransform);
        if (!loadingTransform) return;

        const x = position.x + ((this._bottomCube.length - 1) % ChessBoard.ChessWidth) * loadingTransform.contentSize.width * loadingCube.scale.x;
        const y = position.y - loadingTransform.contentSize.height * loadingCube.scale.y;

        // 创建新方块
        const cubeNode = new Node(`BottomCube_${this._bottomCube.length - 1}`);
        const sprite = cubeNode.addComponent(Sprite);
        const transform = cubeNode.addComponent(UITransform);

        // 设置纹理和位置
        this.setCubeTexture(cubeNode, cube.color);
        cubeNode.setPosition(x, y);
        NodeHelper.setNodeTag(cubeNode, 800 + this._bottomCube.length - 1);
        cubeNode.active = true;

        // 如果是可点击的方块
        if (cube.color === 0) {
            const button = cubeNode.addComponent(Button);
            button.node.on(Button.EventType.CLICK, () => this.onCubeClick(cubeNode), this);
        }

        // 移动动画
        const targetY = this._bottomCube.length === ChessBoard.ChessWidth ? position.y : position.y;
        if (this._bottomCube.length !== ChessBoard.ChessWidth) {
            tween(cubeNode)
                .to(0.1, { position: new Vec3(x, targetY, 0) })
                .start();
        } else {
            cubeNode.setPosition(x, targetY);
        }

        this._root.addChild(cubeNode);
        cubeNode.setSiblingIndex(5);
    }

    /**
     * 移动方块
     */
    private moveCube(): void {
        // TODO: 实现方块移动逻辑
        console.log("移动方块");
    }

    /**
     * 移动方块动作回调
     */
    private moveCubeActionCallBack(): void {
        // 移动完成了，开始流程
        if (this._mainScheduleSpeed >= 1) {
            this.addBoneCube();
            this.findClear();
        }
    }

    /**
     * 查找可消除的方块
     */
    private findClear(): void {
        // TODO: 实现消除逻辑
        console.log("查找可消除的方块");
    }

    /**
     * 加载时间轴调度
     */
    private loadingSchedule(dt: number): void {
        // 判断是否需要倒计时
        const isrun = this.judgeMove(ChessBoard.ChessHeight - 1);
        if (isrun) {
            if (this._second <= 0) {
                // 游戏结束
                this.unscheduleAllCallbacks();
                this.createGameOver();
                return;
            }

            // 更新时间显示
            this.updateTimeDisplay();
            this._second--;
        } else {
            this.resetLoadingBar();
        }
    }

    /**
     * 更新时间显示
     */
    private updateTimeDisplay(): void {
        if (!this._root) return;

        const secondImg = this.findNodeByTag(this._root, MainCommon.Elements.secondImg);
        const loadingBar = this.findNodeByTag(this._root, MainCommon.Elements.timerLoadingBar);
        const secondTxt = this.findNodeByTag(this._root, MainCommon.Elements.secondTxt);

        if (secondTxt) {
            const label = secondTxt.getComponent(Label);
            if (label) {
                label.string = this._second.toString();
            }
        }

        if (loadingBar) {
            const progressBar = loadingBar.getComponent(ProgressBar);
            if (progressBar) {
                progressBar.progress -= 100 / (60 / 1); // 假设初始时间为60秒
            }
        }

        if (secondImg && loadingBar) {
            // 移动秒数图标
            tween(secondImg)
                .to(this._second, { position: new Vec3(loadingBar.position.x, secondImg.position.y, 0) })
                .start();
        }
    }

    /**
     * 创建游戏结束界面
     */
    private createGameOver(): void {
        if (this.gameOverPrefab) {
            this._gameOver = instantiate(this.gameOverPrefab);
            this.node.addChild(this._gameOver);
            this._gameOver.setSiblingIndex(999);

            // 设置游戏结束按钮事件
            this.setupGameOverEvents();
        }
    }

    /**
     * 设置游戏结束事件
     */
    private setupGameOverEvents(): void {
        if (!this._gameOver) return;

        // 新游戏按钮
        const newBtn = this.findNodeByTag(this._gameOver, 169); // gameOverEnum.aginBtn_gameOver
        if (newBtn) {
            const button = newBtn.getComponent(Button);
            if (button) {
                button.node.on(Button.EventType.CLICK, this.onNewGameClick, this);
            }
        }

        // 排行榜按钮
        const rankBtn = this.findNodeByTag(this._gameOver, 172); // gameOverEnum.rankList_gameOver
        if (rankBtn) {
            const button = rankBtn.getComponent(Button);
            if (button) {
                button.node.on(Button.EventType.CLICK, this.onRankListClick, this);
            }
        }

        // 退出按钮
        const escBtn = this.findNodeByTag(this._gameOver, 175); // gameOverEnum.escBtn_gameOver
        if (escBtn) {
            const button = escBtn.getComponent(Button);
            if (button) {
                button.node.on(Button.EventType.CLICK, this.onGameOverEscClick, this);
            }
        }
    }

    /**
     * 排行榜按钮点击
     */
    private onRankListClick(): void {
        console.log("显示排行榜");
    }

    /**
     * 游戏结束退出按钮点击
     */
    private onGameOverEscClick(): void {
        console.log("退出游戏");
    }

}