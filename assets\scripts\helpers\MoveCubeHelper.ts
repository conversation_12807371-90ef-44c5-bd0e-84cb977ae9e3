/**
 * 方块移动辅助类
 * 移植自原MoveCubeHelper.js
 * <AUTHOR>
 */

import { Node, Vec3 } from 'cc';
import EnumHelper from './EnumHelper';
import { NodeHelper } from '../utils/NodeHelper';

interface IJingdian {
    _arr: (number | null)[];
    _bottomCube: number[];
    _root: Node;
}

export default class MoveCubeHelper {
    /**
     * 查找需要移动的方块并执行移动
     * @param jingdian 游戏主逻辑实例
     */
    static findNeedCobeAndMove(jingdian: IJingdian): void {
        // TODO: 实现方块移动逻辑
    }

    /**
     * 查找顶部方块
     * @param jingdian 游戏主逻辑实例
     * @param list 排除列表
     * @param index 起始索引
     * @returns 可移动方块索引列表
     */
    static findTopCube(
        jingdian: IJingdian,
        list: number[],
        index: number
    ): number[] {
        const rtnList: number[] = [];
        const arr = jingdian._arr;

        for (let i = index; i < arr.length; i += EnumHelper.ChessBoard.ChessWidth) {
            if (arr[i] === null) continue;

            let isAdd = true;
            for (const item of list) {
                if (i === item) {
                    isAdd = false;
                    break;
                }
            }

            if (isAdd) rtnList.push(i);
        }
        return rtnList;
    }

    /**
     * 移动方块矩阵
     * @param jingdian 游戏主逻辑实例
     */
    static moveMatrix(jingdian: IJingdian): void {
        const arr = jingdian._arr;
        const bottomeCube = jingdian._bottomCube;
        const layout = jingdian._root;

        if (bottomeCube.length < EnumHelper.ChessBoard.ChessWidth) return;

        for (let i = 0; i < arr.length; i++) {
            if (arr[i] !== null) {
                const cube = NodeHelper.getChildByTag(layout, 900 + i);
                if (cube) {
                    const currentTag = NodeHelper.getNodeTag(cube);
                    NodeHelper.setNodeTag(cube, currentTag + EnumHelper.ChessBoard.ChessWidth);
                }
            }
        }

        for (let i = 0; i < bottomeCube.length; i++) {
            const cube = NodeHelper.getChildByTag(layout, 800 + i);
            if (cube) {
                const currentTag = NodeHelper.getNodeTag(cube);
                NodeHelper.setNodeTag(cube, currentTag + 100);
            }
        }
    }
}