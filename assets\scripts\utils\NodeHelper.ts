/**
 * 节点辅助工具
 * 用于替代原来的 getChildByTag 方法
 * <AUTHOR>
 */

import { Node, Button } from 'cc';

export class NodeHelper {
    /**
     * 根据标签查找子节点
     * 在 Cocos Creator 3.x 中，使用名称来模拟标签功能
     */
    static getChildByTag(parent: Node, tag: number): Node | null {
        const tagName = `tag_${tag}`;
        return parent.getChildByName(tagName);
    }

    /**
     * 设置节点的标签（通过名称）
     */
    static setNodeTag(node: Node, tag: number): void {
        node.name = `tag_${tag}`;
    }

    /**
     * 获取节点的标签
     */
    static getNodeTag(node: Node): number {
        const match = node.name.match(/^tag_(\d+)$/);
        return match ? parseInt(match[1]) : -1;
    }

    /**
     * 查找所有具有指定标签前缀的子节点
     */
    static getChildrenByTagPrefix(parent: Node, tagPrefix: string): Node[] {
        const children: Node[] = [];
        parent.children.forEach(child => {
            if (child.name.startsWith(tagPrefix)) {
                children.push(child);
            }
        });
        return children;
    }

    /**
     * 递归查找具有指定标签的节点
     */
    static findChildByTagRecursive(parent: Node, tag: number): Node | null {
        const tagName = `tag_${tag}`;

        // 先在直接子节点中查找
        const directChild = parent.getChildByName(tagName);
        if (directChild) return directChild;

        // 递归查找
        for (const child of parent.children) {
            const found = this.findChildByTagRecursive(child, tag);
            if (found) return found;
        }

        return null;
    }

    /**
     * 根据标签查找按钮组件
     */
    static findButtonByTag(parent: Node, tag: number): Button | null {
        const node = this.findChildByTagRecursive(parent, tag);
        return node ? node.getComponent(Button) : null;
    }

    /**
     * 递归查找具有指定标签的按钮组件（备用方法）
     */
    static findButtonByTagRecursive(node: Node, tag: number): Button | null {
        // 检查当前节点是否有 Button 组件且标签匹配
        const button = node.getComponent(Button);
        if (button && this.getNodeTag(node) === tag) {
            return button;
        }

        // 递归查找子节点
        for (const child of node.children) {
            const result = this.findButtonByTagRecursive(child, tag);
            if (result) {
                return result;
            }
        }

        return null;
    }
}
