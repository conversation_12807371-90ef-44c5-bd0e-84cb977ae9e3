'use strict';
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GltfImporter = void 0;
const base_1 = require("../common/base");
class GltfImporter extends base_1.ImporterBase {
    constructor() {
        super(...arguments);
        this.type = 'base';
    }
    import() {
        return __awaiter(this, void 0, void 0, function* () {
            this._3dMeta.ver = '2.0.8';
            this._3dMeta.importer = 'gltf';
            this._3dMeta.userData.imageMetas = [];
            this._3dMeta.userData.legacyFbxImporter = false;
            this._3dMeta.userData.disableMeshSplit = true;
            return true;
        });
    }
}
exports.GltfImporter = GltfImporter;
