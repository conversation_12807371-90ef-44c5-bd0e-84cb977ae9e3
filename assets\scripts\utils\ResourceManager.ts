/**
 * 资源管理器
 * 移植自原RES.js
 * <AUTHOR>
 */

import { SpriteFrame, resources } from 'cc';

export class ResourceManager {
    private static dogSprites: { [key: number]: SpriteFrame } = {};
    private static hintSprites: { [key: string]: SpriteFrame } = {};

    /**
     * 获取狗狗方块精灵
     */
    static GET_DOG(colorIndex: number): SpriteFrame | null {
        if (this.dogSprites[colorIndex]) {
            return this.dogSprites[colorIndex];
        }

        // 这里应该根据实际的资源路径来加载
        // 暂时返回 null，需要根据项目实际资源结构调整
        const spritePath = `textures/dog/dog${colorIndex}`;
        
        resources.load(spritePath, SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                this.dogSprites[colorIndex] = spriteFrame;
            }
        });

        return this.dogSprites[colorIndex] || null;
    }

    /**
     * 获取提示相关精灵
     */
    static get HINT_COMBO_OFF(): SpriteFrame | null {
        return this.getHintSprite('combo_off');
    }

    static get HINT_READY(): SpriteFrame | null {
        return this.getHintSprite('ready');
    }

    static get HINT_GO(): SpriteFrame | null {
        return this.getHintSprite('go');
    }

    private static getHintSprite(name: string): SpriteFrame | null {
        if (this.hintSprites[name]) {
            return this.hintSprites[name];
        }

        const spritePath = `textures/hints/${name}`;
        resources.load(spritePath, SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                this.hintSprites[name] = spriteFrame;
            }
        });

        return this.hintSprites[name] || null;
    }

    /**
     * 预加载所有资源
     */
    static preloadResources(): void {
        // 预加载狗狗精灵
        for (let i = 0; i <= 10; i++) {
            this.GET_DOG(i);
        }

        // 预加载提示精灵
        this.HINT_COMBO_OFF;
        this.HINT_READY;
        this.HINT_GO;
    }
}

// 全局 RES 对象，保持向后兼容
export const RES = ResourceManager;
