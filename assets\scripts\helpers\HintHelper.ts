/**
 * 提示辅助类
 * 移植自原HintHelper.js
 * <AUTHOR>
 */

import { _decorator, Component, Node, ProgressBar, Sprite, tween, Vec3, Label } from 'cc';
import EnumHelper from './EnumHelper';
const { ccclass, property } = _decorator;

interface IJingdian {
    _hint: Node;
    _currentCombo: number;
    _maxCombo: number;
    comboActionOver: () => void;
}

export default class HintHelper {
    /**
     * 设置连击提示
     * @param jingdian 游戏主逻辑实例
     */
    public static async setCombo(jingdian: IJingdian): Promise<void> {
        const hint = jingdian._hint;
        jingdian._currentCombo++;

        if (jingdian._currentCombo > jingdian._maxCombo) {
            jingdian._maxCombo = jingdian._currentCombo;
        }

        if (jingdian._currentCombo > 1) {
            hint.active = true;
        }

        const leftOld = hint.getChildByName('progressBar_110');
        if (leftOld) {
            leftOld.destroy();
        }

        const hintImg = hint.getChildByName('hintImg');
        if (!hintImg) return;

        // 更新连击数显示
        const hintLB = hint.getChildByName('hintLabelAtlas');
        if (hintLB && hintLB.getComponent(Label)) {
            hintLB.getComponent(Label).string = jingdian._currentCombo.toString();
            hintLB.active = true;
        }

        // 创建进度条效果
        const progressBarNode = new Node('progressBar_110');
        hint.addChild(progressBarNode);
        const progressBar = progressBarNode.addComponent(ProgressBar);
        progressBar.progress = 0;

        tween(progressBar)
            .to(3, { progress: 1 })
            .call(() => {
                jingdian.comboActionOver();
                progressBar.destroy();
            })
            .start();

        // 设置进度条位置和层级
        progressBar.node.position = hintImg.position.clone();
        progressBar.node.scale = hintImg.scale.clone();
        progressBar.node.setSiblingIndex(hintImg.getSiblingIndex() + 1);
    }
}