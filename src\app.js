
var MainMenuLayer = cc.Layer.extend({
    _root:null,
    _btnLayout:null,
    ctor:function () {
        //////////////////////////////
        // 1. super init first
        this._super();

        /////////////////////////////
        // 2. add a menu item with "X" image, which is clicked to quit the program
        //    you may modify it.
        // ask the window size
        var size = cc.winSize;

        this._root = ccs.load(res.GameMenu_json).node;
        this.addChild(this._root, 0);

        var settingBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainMenuEnum.settingBtn);
        settingBtn.addTouchEventListener(this.touchEvent, this);

        var rankListBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainMenuEnum.rankListBtn);
        rankListBtn.addTouchEventListener(this.touchEvent, this);

        var shopBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainMenuEnum.shopBtn);
        shopBtn.addTouchEventListener(this.touchEvent, this);

        var jingdianBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainMenuEnum.jingdianBtn);
        jingdianBtn.addTouchEventListener(this.touchEvent, this);

        //bool saveLoadBool = XMLHelper::getSaveLoadStatic("scripture.xml");
        //if (saveLoadBool)
        //{
        //    jingdianBtn->addTouchEventListener(CC_CALLBACK_2(MainMenu::newGameBtn_click, this));
        //}
        //else
        //{
        //    jingdianBtn->addTouchEventListener(CC_CALLBACK_2(MainMenu::jingdianBtn_click, this));
        //}

        this._btnLayout = ccs.load(res.GameMenu2_json).node;
        this._btnLayout.setVisible(false);
        this.addChild(this._btnLayout, 1);

        var backBtn = ccui.helper.seekWidgetByTag(this._btnLayout, EnumHelper.gameMenu2.backBtns);
        backBtn.addTouchEventListener(this.touchEvent, this);

        var newGame = ccui.helper.seekWidgetByTag(this._btnLayout, EnumHelper.gameMenu2.newGameBtn);
        newGame.addTouchEventListener(this.touchEvent, this);

        var goonGame = ccui.helper.seekWidgetByTag(this._btnLayout, EnumHelper.gameMenu2.GoonGameBtn);
        goonGame.addTouchEventListener(this.touchEvent, this);

        this.bake();

        return true;
    },
    touchEvent: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;

        switch (sender.getTag()) {
            case EnumHelper.MainMenuEnum.settingBtn:
                cc.director.runScene(new SettingScene());
                break;
            case EnumHelper.MainMenuEnum.rankListBtn:
                cc.director.runScene(new RankListScene());
                break;
            case EnumHelper.MainMenuEnum.shopBtn:
                cc.director.runScene(new ShopScene());
                break;
            case EnumHelper.MainMenuEnum.jingdianBtn:
                //this._btnLayout.setVisible(true);
                cc.director.runScene(new JingdianScene());
                break;
            case EnumHelper.gameMenu2.backBtns:
                this._btnLayout.setVisible(false);
                break;
            case EnumHelper.gameMenu2.newGameBtn:
                cc.director.runScene(new JingdianScene());
                break;
            case EnumHelper.gameMenu2.GoonGameBtn:
                cc.director.runScene(new JingdianScene());
                break;
            default:
                break;
        }
    }
});

var MainMenuScene = cc.Scene.extend({
    onEnter:function () {
        this._super();
        var layer = new MainMenuLayer();
        this.addChild(layer);
    }
});

