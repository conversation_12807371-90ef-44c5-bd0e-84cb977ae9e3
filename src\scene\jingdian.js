/**
 * Created by <PERSON><PERSON> on 15/8/22.
 */

var JingdianLayer = cc.Layer.extend({
    _goonGame: null,                //是否为继续游戏
    _arr: null,                     //矩阵列表 std.vector<CustomArray*>
    _willCube: null,                //初始化预选则色块列表 std.vector<CustomArray*>
    _bottomCube: null,              //底部色块列表 std.vector<CustomArray*>
    _cubeLength: null,              //初始化可选色块的长度
    _line: null,                    //初始化加载行数
    _indexs: null,                  //消除列表容器 std.vector<std.vector<int>>
    _second: null,                  //秒数
    _root: null,
    _bgLayout: null,
    _gameOver: null,
    _playLayout: null,
    _finishAnimationCount: null,    //已完成动画的个数
    _mainScheduleSpeed: null,       //底部初始加载速度
    _pauseLayout: null,             //暂停界面
    _hint: null,                    //提示信息
    _mu: null,                      //遮罩透明幕
    _willCubeVec2: null,            //willCube Vec2 第一个加载色块的位置, 用于定位
    _maxCombo: null,                //最高连击
    _currentCombo: null,            //当前连击
    _comboSecond: null,
    _clearEffSize: null,            //std.vector<int>
    ctor: function () {
        // 1. super init first
        this._super();

        this._goonGame = false;

        this._mainScheduleSpeed = 0.1;
        this._line = 3;
        this._cubeLength = EnumHelper.CommonEnum.minCubeArrayLength;
        this._willCube = LodingCube.lodingCube(this._cubeLength, false, 3);
        this._root = ccs.load(res.MainCommon_json).node;
        this._hint = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.hintLayout);

        this._bgLayout = ccs.load(res.JingdianLayout_json).node;
        this._pauseLayout = ccs.load(res.MainCommon2_json).node;
        this._arr = []; //new std.vector<CustomArray*>(EnumHelper.ChessHeight* EnumHelper.ChessWidth);
        this._bottomCube = []; //new std.vector<CustomArray*>();
        this._second = EnumHelper.CommonEnum.jingdianSecond;
        this._maxCombo = 0;
        this._currentCombo = 0;
        this._willCubeVec2 = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.willImg1).getPosition();

        this._playLayout = new ccui.Layout();
        this._playLayout.setTouchEnabled(false);

        //var bake = cc.Layer.create();
        //this.addChild(bake, 0);
        //bake.addChild(this._bgLayout);

        this.addChild(this._bgLayout, 0);
        this.addChild(this._root);
        this.addChild(this._playLayout, 99999);
        this.addChild(this._pauseLayout, 999);
        this._pauseLayout.setVisible(false);

        //加载动画文件
        ccs.armatureDataManager.addArmatureFileInfo(res.CubeAnimation_anim0_png, res.CubeAnimation_anim0_plist, res.CubeAnimation_anim);
        //this._finishAnimationCount = 0;

        /** 初始配置 **/
        var greenFloor = ccui.helper.seekWidgetByTag(this._root, 395);
        greenFloor.setLocalZOrder(999999);

        //一格时间图标
        var clockSmall = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.clockSmall);
        clockSmall.setVisible(false);

        //一格线
        var dottedLine1 = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.dottedLine1);
        dottedLine1.setVisible(false);

        var bottomViel1 = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.bottomVeil1Img);//木板
        bottomViel1.setVisible(false);
        var bottomViel = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.bottomVeil2Img);//小草
        bottomViel.setTouchEnabled(true);

        //获取矩阵第一个位置
        var startCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.startPoint);
        startCube.setVisible(false);

        //获取加载行第一个位置
        var lodingCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.loadingPoint);
        lodingCube.setVisible(false);

        //向上提示的箭头
        var arrows = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.arrowsUpLayout);
        arrows.setLocalZOrder(10);
        arrows.setVisible(false);

        var readyGo = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.readyGoPanel);
        readyGo.setVisible(false);

        this._hint.setParent(this);
        this._hint.setLocalZOrder(10000);
        this._hint.setVisible(false);
        /** end **/

        // 暂停按钮
        var backBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.pauseBtn);
        backBtn.addTouchEventListener(this.backBtn_click, this);

        // 时间道具
        var timeItem = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.propsImg1);
        timeItem.addTouchEventListener(function(sender, type){
            if (type == ccui.Widget.TOUCH_ENDED)
                this.resetLoadingBar();
            //this._second += 10;
            //if (this._second >= EnumHelper.CommonEnum.jingdianSecond)
            //    this._second = EnumHelper.CommonEnum.jingdianSecond;
        }, this);

        // 分数
        var txt = ccui.helper.seekWidgetByTag(this._bgLayout, EnumHelper.jingdianEnum.fenshuLabel);
        txt.setString("0");
        txt.setTextHorizontalAlignment(cc.TEXT_ALIGNMENT_CENTER);

        // 等级
        var level = ccui.helper.seekWidgetByTag(this._bgLayout, EnumHelper.jingdianEnum.levelLabelAtlas);
        level.setString("0");
        level.setContentSize(level.getVirtualRendererSize());

        //设置willImg的色块颜色
        JingdianHelper.willImgHelper(this._root, this._willCube);

        if (this._goonGame) {
            //XMLHelper.getScripture(arr, this._bottomCube, willCube, second);
        }

        this.schedule(this.mainSchedule, this._mainScheduleSpeed);
        JingdianActionHelper.readyGoAction(this, EnumHelper.ActionType.readyHint);

        /** 暂停按钮内的事件 **/
        //保存并退出
        var btn_SaveESC = ccui.helper.seekWidgetByTag(this._pauseLayout, EnumHelper.MainCommon2.saveBack_Btn);
        btn_SaveESC.addTouchEventListener(this.saveEsc_click, this);
        //新游戏
        var btn_PauseNew = ccui.helper.seekWidgetByTag(this._pauseLayout, EnumHelper.MainCommon2.new_btn);
        btn_PauseNew.addTouchEventListener(this.gameOver_new, this);
        //继续游戏
        var btn_pauseGoon = ccui.helper.seekWidgetByTag(this._pauseLayout, EnumHelper.MainCommon2.goon_btn);
        btn_pauseGoon.addTouchEventListener(this.goonGame_click, this);
        /** end **/

        /** 设置遮罩幕 **/
        this._mu = new ccui.Layout();
        //this._mu.setBackGroundColorType(ccui.Layout.BG_COLOR_SOLID);
        //this._mu.setBackGroundColor(EnumHelper.BLACK);
        this._mu.setSize(cc.winSize);
        this._mu.setPosition(cc.p(0, 0));
        this._mu.setTouchEnabled(true);
        //this._mu.setBackGroundColorOpacity(100);
        this._mu.setVisible(false);
        this.addChild(this._mu, 3);
        /** end **/

        this.bake();
    },
    createGameOver: function() {
        this._gameOver = ccs.load(res.Gameover_json).node;
        this.addChild(this._gameOver, 999);
        //结束面板新游戏
        var btn_new = ccui.helper.seekWidgetByTag(this._gameOver, EnumHelper.gameOverEnum.aginBtn_gameOver);
        btn_new.addTouchEventListener(this.gameOver_new, this);
        //排行榜
        var btn_rank = ccui.helper.seekWidgetByTag(this._gameOver, EnumHelper.gameOverEnum.rankList_gameOver);
        btn_rank.addTouchEventListener(this.gameOver_rank, this);
        //退出
        var btn_Esc = ccui.helper.seekWidgetByTag(this._gameOver, EnumHelper.gameOverEnum.escBtn_gameOver);
        btn_Esc.addTouchEventListener(this.gameOver_Esc, this);
    },
    onEnter: function () {
        this._super();
        //ccs.armatureDataManager.addArmatureFileInfo(res.CubeAnimation_anim0_png, res.CubeAnimation_anim0_plist, res.CubeAnimation_anim);
        //ccs.armatureDataManager.addArmatureFileInfo(res.CubeAnimation_anim);
    },
    mainSchedule: function (dt) {
        var d = Math.floor(dt);//当前schedule的时间
        var isrun = this.judgeMove(this._line);//false;
        if (isrun) {
            if (d == 0) {
                //TODO.初次加载完成, GO动画
                JingdianActionHelper.readyGoAction(this, EnumHelper.ActionType.goHint);

                this.unschedule(this.mainSchedule);
                this.addBoneCube();
                this._mainScheduleSpeed = 1;
                this.schedule(this.mainSchedule, this._mainScheduleSpeed);
                this.schedule(this.loadingSchedule, this._mainScheduleSpeed);
                return;
            }
        }

        if (this._bottomCube.length >= EnumHelper.ChessBoard.ChessWidth) {
            isrun = this.judgeMove(EnumHelper.ChessBoard.ChessHeight);
            if (!isrun) {
                this.moveCube();
            }
        }
        else {
            var cube = LodingCube.lodingCube(this._cubeLength, true);
            this._bottomCube.push(cube);
            this.createCube(cube);
        }
    },
    readyGoCallBack: function () {
        var panel = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.readyGoPanel);
        var img = ccui.helper.seekWidgetByTag(panel, EnumHelper.MainCommonEnum.readyGoImg);
        img.setScale(0.8);
        panel.setVisible(false);
    },
    loadingSchedule: function (dt) {
        //判断是否需要倒计时
        var isrun = this.judgeMove(EnumHelper.ChessBoard.ChessHeight - 1);
        if (isrun) {
            if (this._second <= 0) {
                //游戏结束
                this.unscheduleAllCallbacks();
                this.createGameOver();
                JingdianHelper.setGameOverLayout(this);
            }

            //不能上涌了,  倒计时游戏结束
            var secondImg = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.secondImg);//秒数
            var loadingBar = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.timerLoadingBar);//时间轴
            var secondTxt = ccui.helper.seekWidgetByTag(secondImg, EnumHelper.MainCommonEnum.secondTxt);//时间标签
            secondTxt.setString(this._second);
            secondTxt.setContentSize(secondTxt.getVirtualRendererSize());

            var point = cc.p(loadingBar.getPositionX(), secondImg.getPositionY());
            //cc.log("%d", point.x);
            var act = cc.moveTo(this._second, point);
            secondImg.stopAllActions();
            secondImg.runAction(act);

            this._second--;
            loadingBar.setPercent(loadingBar.getPercent() - 100 / (EnumHelper.CommonEnum.jingdianSecond / dt));
        }
        else {
            this.resetLoadingBar();
        }
    },
    gameOverMethod: function (dt) {
        var loadingBar = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.timerLoadingBar);//时间轴
        loadingBar.setPercent(loadingBar.getPercent() - 100 / (EnumHelper.CommonEnum.jingdianSecond / dt));
    },
    resetLoadingBar: function () {
        var secondImg = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.secondImg);//秒数
        var loadingBar = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.timerLoadingBar);//时间轴
        var secondTxt = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.secondTxt);//时间标签

        this._second = EnumHelper.CommonEnum.jingdianSecond;
        secondTxt.setString(this._second);
        secondTxt.setContentSize(secondTxt.getVirtualRendererSize());

        loadingBar.setPercent(100);
        secondImg.setPosition(loadingBar.getPosition().x + loadingBar.getContentSize().width * loadingBar.getScaleX(), secondImg.getPosition().y);
        secondImg.stopAllActions();
        //this.unschedule(this.gameOverMethod);
    },
    comboSchedule: function (dt) {
        var to1 = cc.progressTo(3, 100);
        if (this._comboSecond == 0) {
            this._currentCombo = 0;
            return;
        }
        this._comboSecond--;
    },
    findClear: function (dt) {
        this._mu.setVisible(true);
        //clearIsRun = true;
        var isStop = false;
        if (this._bottomCube.length >= EnumHelper.ChessBoard.ChessWidth) {
            isStop = true;
            this.unschedule(this.mainSchedule);
        }

        this._indexs = [];
        this._clearEffSize = [];
        Logic.method(this._arr, this._indexs);

        if (this._indexs.length == 0) {
            this._mu.setVisible(false);
            if (isStop)
                this.schedule(this.mainSchedule, this._mainScheduleSpeed);
            return;
        }

        //设置连击
        HintHelper.setCombo(this);
        var firstList = this._indexs[0];
        this._clearEffSize.push(EnumHelper.Effects.nullEff);

        var efflist;

        for (var i = 0; i < firstList.length; i++) {
            /** 消除中包含eff 4 **/
            if (this._arr[firstList[i]].eff == EnumHelper.Effects.fourEff) {
                efflist = []; //std.vector<int>
                //当前色块一下的
                var effIndex = firstList[i] - EnumHelper.ChessBoard.ChessWidth;
                while (effIndex >= 0) {
                    var iscontinue = false;
                    for (var h = 0; h < firstList.length; h++) {
                        if (effIndex == firstList[h]) {
                            iscontinue = true;
                        }
                    }
                    if (iscontinue) {
                        effIndex -= EnumHelper.ChessBoard.ChessWidth;
                        continue;
                    }
                    //TODO:当前色块一下的所有色块设置成黑色
                    var effImg = ccui.helper.seekWidgetByTag(this._root, 900 + effIndex);
                    if (effImg) {
                        effImg.loadTexture(RES.GET_DOG(8), ccui.Widget.PLIST_TEXTURE);
                        //加黑烟效果
                        AnimationHelper.addClearCube(this, effImg, effIndex, true, 2.5, 10);
                    }
                    efflist.push(effIndex);
                    effIndex -= EnumHelper.ChessBoard.ChessWidth;
                }
                if (efflist.length > 0) {
                    this._indexs.push(efflist);
                    this._clearEffSize.push(EnumHelper.fourEff);
                }
            }
            /** end **/

            /** 消除中包含eff 5 **/
            if (this._arr[firstList[i]].eff == EnumHelper.Effects.fiveEff) {
                var fivePassively = []; //std.vector<int>
                //以firstList[i]的坐标为原点,
                AboutHelper.find_fivePassively(firstList[i], this._arr, fivePassively, this);
                this._indexs.push(fivePassively);
                this._clearEffSize.push(EnumHelper.Effects.fiveEff);
                this._indexs[0] = [];
            }
            /** end **/

            /** 消除中包含eff 6 **/
            if (this._arr[firstList[i]].eff >= EnumHelper.Effects.sixEff) {
                for (var effi = EnumHelper.ChessWidth * EnumHelper.ChessHeight - 1; effi >= 0; effi--) {
                    if (this._arr[effi] == null) {
                        continue;
                    }
                    if (this._arr[effi].color == this._arr[firstList[i]].color)//查找颜色相同的色块
                    {
                        var isadd = true;
                        for (var b = 0; b < firstList.length; b++) {
                            if (firstList[b] == effi) {
                                isadd = false;//判断找到的色块是不是在firstList 中, 如果不在才添加
                                break;
                            }
                        }
                        if (isadd) {
                            efflist = []; //std.vector<int>
                            //efflist.push(effi);
                            this.sixPassivetyPartical(effi, efflist);

                            //top
                            var passivityIndex = effi + EnumHelper.ChessWidth;
                            if (passivityIndex < EnumHelper.ChessHeight * EnumHelper.ChessWidth && this._arr[passivityIndex] != null) {
                                this.sixPassivetyPartical(passivityIndex, efflist);
                            }
                            //bottom
                            passivityIndex = effi - EnumHelper.ChessWidth;
                            if (passivityIndex > 0 && this._arr[passivityIndex] != null) {
                                this.sixPassivetyPartical(passivityIndex, efflist);
                            }
                            //left
                            passivityIndex = effi - 1;
                            if (effi % EnumHelper.ChessWidth != 0 && this._arr[passivityIndex] != null) {
                                this.sixPassivetyPartical(passivityIndex, efflist);
                            }
                            passivityIndex = effi + 1;
                            if (passivityIndex % EnumHelper.ChessWidth != 0 && this._arr[passivityIndex] != null) {
                                this.sixPassivetyPartical(passivityIndex, efflist);
                            }

                            if (efflist.length > 0) {
                                this._indexs.push(efflist);
                                this._clearEffSize.push(EnumHelper.sixEff);
                            }
                        }
                    }
                }
            }
            /** end **/
        }

        if (firstList.length == 3) {
            this.addThreeAnimation();
        }
        if (firstList.length >= 4) {
            this.addFourAnimation();
        }
    },
    comboActionOver: function () {
        this._currentCombo = 0;
        this._hint.setVisible(false);
    },
    createCube: function (cube) {
        var loadingCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.loadingPoint);
        var position = loadingCube.getPosition();
        var x = position.x + (this._bottomCube.length - 1) % EnumHelper.ChessBoard.ChessWidth * loadingCube.getContentSize().width * loadingCube.getScale();
        var y = position.y - loadingCube.getContentSize().height * loadingCube.getScaleY();

        var addCube = loadingCube.clone();
        //var img = "res/dog/dog" + cube.color + ".png";
        var img = RES.GET_DOG(cube.color);
        addCube.loadTexture(img, ccui.Widget.PLIST_TEXTURE);
        addCube.setPosition(x, y);
        addCube.setTag(800 + this._bottomCube.length - 1);
        addCube.setVisible(true);
        if (cube.color == 0) {
            addCube.setTouchEnabled(true);
            addCube.addTouchEventListener(this.clicks, this);
        }

        if (this._bottomCube.length == EnumHelper.ChessBoard.ChessWidth) {
            addCube.setPosition(x, position.y);
        }
        else {
            var act = cc.moveTo(0.1, cc.p(x, position.y));
            addCube.runAction(act);
        }
        this._root.addChild(addCube, 5);
    },
    animationOver: function (dt) {
        var firstlist = this._indexs[0];
        if (firstlist.length == 3) {
            for (var i = 0; i < firstlist.length; i++) {
                var img = ccui.helper.seekWidgetByTag(this._root, 900 + firstlist[i]);
                if (img) {
                    ////白色星星
                    ParticalHelper.addStar(this, img, res.WhiteStarParticle_plist, 1.5);
                    ////黄金星星
                    ParticalHelper.addStar(this, img, res.GoldStarParticle_plist, 1.5);

                    var json = ccs.load(res.CubeAnimation_json);
                    var node = json.node, action = json.action;
                    node.runAction(action);
                    action.gotoFrameAndPlay(0);
                    node.setPosition(img.getContentSize().width / 2, img.getContentSize().height / 2);
                    node.setTag(600 + firstlist[i]);
                    node.setScale(1.2);
                    img.addChild(node);
                }
            }
        }
        else if (firstlist.length >= 4) {
            var clearImg = ccui.helper.seekWidgetByTag(this._root, 900 + firstlist[0]);
            //删除白色边框 , 添加特效动画
            clearImg.removeAllChildren();
            if (firstlist.length == 4) {
                AnimationHelper.addFire(this, clearImg, firstlist[0], true, 1, 10);
                this._arr[firstlist[0]].eff = EnumHelper.Effects.fourEff;
            }
            if (firstlist.length == 5) {
                AnimationHelper.addSpot(this, clearImg, firstlist[0], true, 1, 10);
                this._arr[firstlist[0]].eff = EnumHelper.Effects.fiveEff;
            }
            if (firstlist.length >= 6) {
                AnimationHelper.addRainbow(this, clearImg, firstlist[0], true, 1, 10);
                this._arr[firstlist[0]].eff = EnumHelper.Effects.sixEff;
            }
            //第一个不消除
            //this._indexs[0].erase(this._indexs[0].begin());
            this._indexs[0].shift();
            this.tagText(clearImg, EnumHelper.GREEN);
        }
        //动画完成的回调结束 , 执行move方法
        this.scheduleOnce(this.animation_TO_moveDecline, 0.1);
    },
    animation_TO_moveDecline: function (dt) {
        if (this._indexs.length == 0) {
            //关闭当前schedule,并执行find方法
            this.moveCube_ActionCallBack();
            return;
        }
        var list = this._indexs[0];

        var effSize = this._clearEffSize[0];
        if (effSize == EnumHelper.Effects.fourEff) {
            //sixPassivetyPartical(list[0], true);
        }
        if (effSize == EnumHelper.Effects.fiveEff) {
            for (var i = 0; i < list.length; i++) {
                AboutHelper.action_five_NoCallBack(this, list[i]);
            }
        }
        if (effSize == EnumHelper.Effects.sixEff) {
            this.sixPassivetyPartical(list[0], null, true);
            for (var j = 1; j < list.length; j++) {
                this.sixPassivetyPartical(list[j], null, false);
            }
        }
        //动画完成的回调结束 , 执行move方法
        this.scheduleOnce(this.moveCube_Decline, 0.1);
    },
    moveCube: function () {
        //clearIsRun = true;
        this._mu.setVisible(true);
        if (this._bottomCube.length != EnumHelper.ChessBoard.ChessWidth) {
            return;
        }

        //加向上箭头
        //JingdianActionHelpler.hintAction(this, EnumHelper.readyHint);
        JingdianActionHelper.arrowsUpAction(this);

        var loadingCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.loadingPoint);

        for (var i = this._arr.length - 1; i >= 0; i--) {
            var img = ccui.helper.seekWidgetByTag(this._root, 900 + i);
            if (img != null) {
                var tag = img.getTag();
                img.setTag(img.getTag() + EnumHelper.ChessBoard.ChessWidth);
                var a = loadingCube.getContentSize().height * loadingCube.getScaleY();
                var y = img.getPosition().y + loadingCube.getContentSize().height * loadingCube.getScaleY();

                AboutHelper.action_moveCube_NoHaveCallBack(0.1, img, cc.p(img.getPosition().x, y));

                this.tagText(img, EnumHelper.BLACK);
            }
        }

        for (i = 0; i < this._bottomCube.length; i++) {
            var imgs = ccui.helper.seekWidgetByTag(this._root, 800 + i);
            if (imgs != null) {
                imgs.setTag(imgs.getTag() + 100);
                var v = cc.p(imgs.getPosition().x, imgs.getPosition().y + loadingCube.getContentSize().height * loadingCube.getScaleY());
                if (i != this._bottomCube.length - 1) {
                    AboutHelper.action_moveCube_NoHaveCallBack(0.1, imgs, v);
                }
                else {
                    AboutHelper.action_moveCube_HaveCallBack(0.1, imgs, v, this);
                }
                this.tagText(imgs, EnumHelper.BLACK);
            }
        }
        for (i = this._bottomCube.length; i > 0; i--) {
            this._arr.unshift(this._bottomCube[i - 1]);
        }
        this._bottomCube = [];
    },
    arrowsUpMoveEvent: function () {
        var arrowsUp = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.arrowsUpLayout);
        arrowsUp.setVisible(false);
    },
    clicks: function (sender, type) {
        if (type == ccui.Widget.TOUCH_ENDED) {
            var s = this._arr;
            var imageView = sender;
            JingdianActionHelper.clickAction(imageView, this);	//移动willCube到点击区域
            ParticalHelper.addStar(this, imageView, res.BubbleParticle_plist, 2.5);//泡泡
            //var str = "res/dog/dog" + this._willCube[0].color + ".png";
            var str = RES.GET_DOG(this._willCube[0].color);
            imageView.loadTexture(str, ccui.Widget.PLIST_TEXTURE);
            imageView.setTouchEnabled(false);
            var tag = imageView.getTag();
            this._arr[tag - 900].color = this._willCube[0].color;//设置矩阵内的颜色
            this.moveCube_ActionCallBack();
        }
    },
    clickActionCallBack: function () {
        var will1 = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.willImg1);
        will1.setPosition(this._willCubeVec2);

        this._willCube.shift();
        this._willCube.push(LodingCube.lodingCube(this._cubeLength, false));
        JingdianHelper.willImgHelper(this._root, this._willCube);
    },
    moveCube_ActionCallBack: function () {
        //移动完成了, 开始流程
        if (this._mainScheduleSpeed >= 1) {
            this.addBoneCube();
            this.findClear(0);
        }
    },
    moveCube_Decline: function (dt) {
        var i;
        var remainder1;
        var remainder2;

        if (this._indexs.length == 0) {
            //关闭当前schedule,并执行find方法
            this.moveCube_ActionCallBack();
            return;
        }
        var clearColumn = []; //找消除列 std.vector<int>
        var list = this._indexs[0];
        //根据当前Combo, 和list的size 设置分数
        if (list.length > 0) {
            JingdianHelper.calcCount(this, list.length * EnumHelper.CommonEnum.score, list[0]);
        }
        for (i = 0; i < list.length; i++) {
            this._root.removeChildByTag(900 + list[i]);
            //CCLOG("%d", list[i]);
            this._arr[list[i]] = null;
            //(*arr)[list[i]] = NULL;

            var isAdd = true;
            for (var cci = 0; cci < clearColumn.length; cci++) {
                remainder1 = clearColumn[cci] % EnumHelper.ChessBoard.ChessWidth;
                remainder2 = list[i] % EnumHelper.ChessBoard.ChessWidth;
                if (remainder1 == remainder2) {
                    isAdd = false;
                }
            }
            if (isAdd) {
                clearColumn.push(list[i]);
            }
        }
        //循环消除列根据每列, 找到上面不消除的的色块
        var loadingCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.loadingPoint);
        var height = loadingCube.getContentSize().height * loadingCube.getScaleY();
        var isRunCallBack = false;
        while (clearColumn.length > 0) {
            var needMoveCube = MoveCubeHelper.findTopCube(this, list, clearColumn[0]);
            //遍历每个需要向下的cube
            for (var n = 0; n < needMoveCube.length; n++) {
                var moveCount = 0;
                remainder1 = needMoveCube[n] % EnumHelper.ChessBoard.ChessWidth;
                for (i = 0; i < list.length; i++) {
                    remainder2 = list[i] % EnumHelper.ChessBoard.ChessWidth;
                    if (remainder1 == remainder2 && needMoveCube[n] > list[i]) {
                        moveCount++;
                    }
                }

                //打印当前消除列, 向下对象, 向下次数
                //cc.log("column:%d ,tag:%d ,count:%d", clearColumn[0], needMoveCube[n], moveCount);

                //添加Action事件
                var needMoveImg = ccui.helper.seekWidgetByTag(this._root, 900 + needMoveCube[n]);
                if (needMoveImg) {
                    var v = cc.p(needMoveImg.getPosition().x, needMoveImg.getPosition().y - moveCount * height);
                    if (clearColumn.length == 1 && n == needMoveCube.length - 1)//该消除的列表都已经消除完成
                    {
                        //有回调的action
                        AboutHelper.action_moveCube_Decline_HaveCallBack(needMoveImg, v, this);
                        isRunCallBack = true;
                    }
                    else {
                        //无回调的action
                        AboutHelper.action_moveCube_Decline_NoCallBack(needMoveImg, v);
                    }
                    needMoveImg.setTag(needMoveImg.getTag() - moveCount * EnumHelper.ChessBoard.ChessWidth);
                    this.tagText(needMoveImg, EnumHelper.GREEN);
                }
            }
            //clearColumn.erase(clearColumn.begin());
            clearColumn.shift();
        }
        if (!isRunCallBack) {
            //没加上callback
            this.scheduleOnce(this.moveCube_Decline_NoCallBack, 0.1);
        }

        //indexs.erase(indexs.begin());
        this._indexs.shift();
        //clearEffSize.erase(clearEffSize.begin());
        this._clearEffSize.shift();
    },
    moveCube_Decline_CallBack: function () {
        Logic.tidyCube(this._arr);
        this.scheduleOnce(this.animation_TO_moveDecline, 0.1);
    },
    moveCube_Decline_NoCallBack: function (dt) {
        Logic.tidyCube(this._arr);
        this.scheduleOnce(this.animation_TO_moveDecline, 0.1);
    },
    tidyMatrix: function () {
        for (var i = 0; i < this._arr.length; i++) {
            var excessCube = ccui.helper.seekWidgetByTag(this._root, 900 + i);
            if (excessCube) {
                if (this._arr[i] == null) {
                    this._root.removeChildByTag(900 + i);
                }
            }
            if (this._arr[i] == null) {
                this._root.removeChildByTag(900 + i);
            }
            else {
                excessCube = ccui.helper.seekWidgetByTag(this._root, 900 + i);
                if (excessCube == null) {
                    var startCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.startPoint);

                    var imageView = startCube.clone();
                    imageView.loadTexture(RES.GET_DOG(this._arr[i].color), ccui.Widget.PLIST_TEXTURE);
                    imageView.setVisible(true);

                    var position = startCube.getPosition();
                    var x = position.x + i % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
                    var y = position.y + i / EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().height * startCube.getScale();

                    imageView.setPosition(x, y);
                    imageView.setTag(900 + i);
                    if (this._arr[i].color == 0)//点击区域算法
                    {
                        imageView.setTouchEnabled(true);
                        imageView.addTouchEventListener(this.clicks, this);
                    }
                    this._root.addChild(imageView, 5);
                    this.tagText(imageView, cc.WHITE);
                }
                else {
                    excessCube.loadTexture(RES.GET_DOG(this._arr[i].color), ccui.Widget.PLIST_TEXTURE);
                }
            }
        }
    },
    calcMoveOver: function () {
        this._playLayout.removeAllChildren();
    },
    judgeMove: function (line) {
        var rtnbool = false;
        if (line < 1) {
            return true;
        }
        for (var i = (line - 1) * EnumHelper.ChessBoard.ChessWidth; i < line * EnumHelper.ChessBoard.ChessWidth; i++) {
            if (this._arr[i] != null) {
                rtnbool = true;
                break;
            }
        }
        return rtnbool;
    },
    addBoneCube: function () {
        var startCube = ccui.helper.seekWidgetByTag(this._root, EnumHelper.MainCommonEnum.startPoint);
        var position = startCube.getPosition();
        var boneList = [];
        Logic.addTopBone(this._arr, boneList);//arr已加载完成,添加色块
        for (var i = 0; i < boneList.length; i++) {
            var x = position.x + boneList[i] % EnumHelper.ChessBoard.ChessWidth * startCube.getContentSize().width * startCube.getScale();
            var y = position.y + Math.floor(boneList[i] / EnumHelper.ChessBoard.ChessWidth) * startCube.getContentSize().height * startCube.getScale();

            //console.log(x + ", " + boneList[i] + ", " + y);
            var img = new ccui.ImageView(RES.GET_DOG(0), ccui.Widget.PLIST_TEXTURE);
            img.setPosition(x, y);
            img.setScale(startCube.getScale() * 0.8);
            img.setTag(900 + boneList[i]);
            var act = cc.scaleTo(0.1, startCube.getScale());
            img.runAction(act);
            img.setTouchEnabled(true);
            img.addTouchEventListener(this.clicks, this);
            this._root.addChild(img, 5);

            this.tagText(img, EnumHelper.RED);
        }
    },
    addThreeAnimation: function () {
        //添加的是 白色方块的 0.1秒 由透明转向不透明的过程
        var firstlist = this._indexs[0];
        if (firstlist.length == 0) {
            //直接跳转
            this.threeCallBack();
        }
        for (var i = 0; i < firstlist.length; i++) {
            var img = ccui.helper.seekWidgetByTag(this._root, 900 + firstlist[i]);
            if (img) {
                if (i == firstlist.length - 1) {
                    AboutHelper.action_Three_HaveCallBack(img, this);
                }
                else {
                    AboutHelper.action_Three_NoCallBack(img);
                }
            }
        }
    },
    threeCallBack: function () {
        this.scheduleOnce(this.animationOver, 0.1);
    },
    sixPassivetyPartical: function (i, efflist, addAnimation) {
        if (arguments.length == 2) {
            var isjump = Logic.isJump(this._indexs, i);
            if (isjump) {
                if (efflist != null) {
                    efflist.push(i);
                }
            }
        } else if (arguments.length == 3) {
            var effImg = ccui.helper.seekWidgetByTag(this._root, 900 + i);
            if (effImg) {
                ParticalHelper.addStar(this, effImg, res.WhiteStarParticle_plist, 2.5);
                if (addAnimation) {
                    AnimationHelper.addStar(this, effImg, 10, true, 1, 11);//放大的金星星
                }
            }
        }
    },
    addFourAnimation: function () {
        //黑烟效果
        var firstlist = this._indexs[0];
        if (firstlist.length == 0) {
            //直接跳转
            this.threeCallBack();
        }
        var firstCube = null;
        for (var i = 0; i < firstlist.length; i++) {
            var img = ccui.helper.seekWidgetByTag(this._root, 900 + firstlist[i]);
            if (img) {
                if (i == 0) {
                    var actImg = AboutHelper.addChildCube(img, RES.GET_DOG(10));
                    firstCube = img;
                    AboutHelper.action_four_HaveCallBack(img, img, this);
                }
                else {
                    AboutHelper.action_four_NoCallBack(img, firstCube, this);
                }
                AnimationHelper.addClude(this, img, firstlist[i], true);
            }
        }
    },
    fiveCallBack: function () {
        this._playLayout.removeAllChildren();
    },
    //animationOver2: function (armature, movementType, movementID) {},
    tagText: function (widget, color) {
        //var del = widget.getChildByTag(88);
        //if (del) {
        //    widget.removeChildByTag(88);
        //}
        //var text = new ccui.Text(widget.getTag(), "宋体", 20);
        //text.setPosition(10, 10);
        //text.setTag(88);
        //text.setColor(color);
        //
        //widget.addChild(text);
    },
    backBtn_click: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        //TODO.暂停所有schedule
        this._pauseLayout.setVisible(true);
    },
    goonGame_click: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        this._pauseLayout.setVisible(false);
    },
    //newGame_click: function (sender, type) {},
    saveEsc_click: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        //XMLHelper.setScripture(arr, this._bottomCube, willCube, second);
        //TODO.保存到xml
        cc.director.runScene(new MainMenuScene());
    },
    gameOver_Esc: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        cc.director.runScene(new MainMenuScene());
    },
    gameOver_new: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        cc.director.runScene(new JingdianScene()); //false
    },
    gameOver_rank: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        cc.director.runScene(new RankListScene());
    }
});

var JingdianScene = cc.Scene.extend({
    onEnter: function () {
        this._super();
        var layer = new JingdianLayer();
        this.addChild(layer);
    }
});