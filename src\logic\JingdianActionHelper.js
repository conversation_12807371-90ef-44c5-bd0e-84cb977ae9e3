/**
 * Created by <PERSON><PERSON> on 15/8/23.
 */

var JingdianActionHelper = {
    clickAction: function (sender, jingdian) {
        var will1 = ccui.helper.seekWidgetByTag(jingdian._root, EnumHelper.MainCommonEnum.willImg1);
        var v = will1.getPosition();
        var action = cc.sequence(
            cc.moveTo(0.1, sender.getPosition()),
            cc.callFunc(jingdian.clickActionCallBack, jingdian));
        will1.runAction(action);
    },
    hintActionMethod: function (layer, type) {
        layer._hint.setVisible(true);
        var visibleSize = cc.winSize;
        layer._hint.setPosition(cc.p(visibleSize.width / 2 - layer._hint.getContentSize().width / 2, visibleSize.height / 2 - layer._hint.getContentSize().height / 2));
        layer._hint.stopAllActions();
        var action = cc.sequence(cc.moveTo(3, cc.p(visibleSize.width / 2 - (layer._hint).getContentSize().width / 2, visibleSize.height / 2 - layer._hint.getContentSize().height / 2 + 100)));
        layer._hint.runAction(action);
    },
    hintAction: function (layer, type) {
        var labelAtlas = layer._hint.getChildByTag(EnumHelper.MainCommonEnum.hintLabelAtlas);
        labelAtlas.setVisible(false);
        var img = ccui.helper.seekWidgetByTag(layer._hint, EnumHelper.MainCommonEnum.hintImg);
        switch (type) {
            case EnumHelper.ActionType.comboHint:
                labelAtlas.setVisible(true);
                img.loadTexture(RES.HINT_COMBO_OFF, ccui.Widget.PLIST_TEXTURE);
                this.hintActionMethod(layer, EnumHelper.ActionType.comboHint);
                break;
            case EnumHelper.ActionType.readyHint:
                img.loadTexture(RES.HINT_READY, ccui.Widget.PLIST_TEXTURE);
                this.hintActionMethod(layer, EnumHelper.ActionType.readyHint);
                break;
            case EnumHelper.ActionType.goHint:
                img.loadTexture(RES.HINT_GO, ccui.Widget.PLIST_TEXTURE);
                this.hintActionMethod(layer, EnumHelper.ActionType.goHint);
                break;
            default:
                break;
        }
    },
    readyGoAction: function (layer, type) {
        var panel = ccui.helper.seekWidgetByTag(layer._root, EnumHelper.MainCommonEnum.readyGoPanel);
        panel.setVisible(true);
        var img = ccui.helper.seekWidgetByTag(panel, EnumHelper.MainCommonEnum.readyGoImg);
        switch (type) {
            case EnumHelper.ActionType.readyHint:
                img.loadTexture(RES.HINT_READY, ccui.Widget.PLIST_TEXTURE);
                img.setContentSize(img.getVirtualRendererSize());
                this.readyGoActionMethods(layer, 1);
                break;
            case EnumHelper.ActionType.goHint:
                img.loadTexture(RES.HINT_GO, ccui.Widget.PLIST_TEXTURE);
                img.setContentSize(img.getVirtualRendererSize());
                this.readyGoActionMethods(layer, 0.5);
                break;
            default:
                break;
        }
    },
    readyGoActionMethods: function (layer, second) {
        var acts = cc.sequence(cc.scaleBy(second, 1.2), cc.callFunc(layer.readyGoCallBack, layer));
        var img = ccui.helper.seekWidgetByTag(layer._root, EnumHelper.MainCommonEnum.readyGoImg);
        img.runAction(acts);
    },
    comboAction: function (layer) {
        this.hintAction(layer, EnumHelper.ActionType.comboHint);
    },
    arrowsUpAction: function (layer) {
        var arrowsUp = ccui.helper.seekWidgetByTag(layer._root, EnumHelper.MainCommonEnum.arrowsUpLayout);
        arrowsUp.setVisible(true);
        var p = arrowsUp.getPosition();
        var action = cc.sequence(
            cc.moveBy(0.5, 0, 50),
            cc.callFunc(function(){
                arrowsUp.setPosition(p);
                arrowsUp.setVisible(false);
            }));
        arrowsUp.runAction(action);
    }
};
