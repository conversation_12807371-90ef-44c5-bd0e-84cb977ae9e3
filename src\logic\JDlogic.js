/**
 * Created by <PERSON><PERSON> on 15/8/23.
 */

var CustomArray = {
    color: null,
    //定义色块的使用次数,双消时使用
    statics: null,
    eff: null
};

var Logic = {
    method: function (arr, indexs) {
        for (var i = 0; i < arr.length; i++) {
            var list = [];
            this.globelFind(arr, indexs, list, i);
            if (list.length >= 3) {
                indexs.push(list);
                return;
            }
        }
    },
    globelFind: function (arr, indexs, list, i) {
        if (arr[i] == null) {
            return false;
        }
        //跳过可点击
        if (arr[i].color == 0) {
            return false;
        }
        //判断i是否以做过处理
        if (!this.isJump(indexs, i))//判断全局消除列表是否添加过当前索引, 不包括当前消除列表
        {
            return false;
        }

        if (this.tryAdd(list, i))//判断当前消除列表是否添加当前索引
        {
            return false;
        }
        //list.push(i);
        //左边
        if (i % EnumHelper.ChessBoard.ChessWidth != 0)//本行第一个,不判断左边
        {
            if (i - 1 >= 0) {
                if (arr[i - 1] != null) {
                    if (arr[i].color == arr[i - 1].color) {
                        this.runLogic(arr, indexs, list, i - 1);
                    }
                }
                //起始位置判断
            }
        }

        //右边
        if ((i + 1) % EnumHelper.ChessBoard.ChessWidth != 0)//到本行的最后一个
        {
            if (arr[i + 1] != null && i + 1 <= EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight)//不是到最上面一行最后一个
            {
                if (arr[i].color == arr[i + 1].color) {
                    this.runLogic(arr, indexs, list, i + 1);
                }
                //right一列判断
            }
        }

        //上边
        if (i + EnumHelper.ChessBoard.ChessWidth < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight) {//判断上面一行是否存在
            if (arr[i + EnumHelper.ChessBoard.ChessWidth] != null)//判断上一行是否为null
            {
                if (arr[i].color == arr[i + EnumHelper.ChessBoard.ChessWidth].color) {
                    this.runLogic(arr, indexs, list, i + EnumHelper.ChessBoard.ChessWidth);
                }
                //top一行判断
            }
        }


        //下边
        if (i - EnumHelper.ChessBoard.ChessWidth >= 0)//判断下面一行是否存在
        {
            if (arr[i - EnumHelper.ChessBoard.ChessWidth] != null) {
                if (arr[i].color == arr[i - EnumHelper.ChessBoard.ChessWidth].color) {
                    this.runLogic(arr, indexs, list, i - EnumHelper.ChessBoard.ChessWidth);
                }
                //bottom一行判断
            }
        }
    },
    tryAdd: function (indexs, index) {
        var isAdd = false;
        for (var i = 0; i < indexs.length; i++) {
            if (indexs[i] == index) {
                isAdd = true;
            }
        }
        if (!isAdd) {
            indexs.push(index);
        }
        return isAdd;
    },
    isJump: function (indexs, index) {
        for (var i = 0; i < indexs.length; i++) {
            for (var j = 0; j < indexs[i].length; j++) {
                if (indexs[i][j] == index) {
                    return false;
                }
            }
        }
        return true;
    },
    runLogic: function (arr, indexs, list, i) {
        //判断i是否以做过处理
        if (!this.isJump(indexs, i))//判断全局消除列表是否添加过当前索引, 不包括当前消除列表
        {
            return false;
        }

        if (this.tryAdd(list, i))//判断当前消除列表是否添加当前索引
        {
            return false;
        }

        if (arr[i] == null) {
            return false;
        }

        //list.push(i);
        //左边
        if (i % EnumHelper.ChessBoard.ChessWidth != 0)//本行第一个,不判断左边
        {
            if (i - 1 >= 0) {
                if (arr[i - 1] != null) {
                    if (arr[i].color == arr[i - 1].color) {
                        this.runLogic(arr, indexs, list, i - 1);
                    }
                }
                //起始位置判断
            }
        }

        //右边
        if ((i + 1) % EnumHelper.ChessBoard.ChessWidth != 0)//到本行的最后一个
        {
            if (arr[i + 1] != null && i + 1 <= EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight)//不是到最上面一行最后一个
            {
                if (arr[i].color == arr[i + 1].color) {
                    this.runLogic(arr, indexs, list, i + 1);
                }
                //right一列判断
            }
        }

        //上边
        if (i + EnumHelper.ChessBoard.ChessWidth < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight) {//判断上面一行是否存在
            if (arr[i + EnumHelper.ChessBoard.ChessWidth] != null)//判断上一行是否为null
            {
                if (arr[i].color == arr[i + EnumHelper.ChessBoard.ChessWidth].color) {
                    this.runLogic(arr, indexs, list, i + EnumHelper.ChessBoard.ChessWidth);
                }
                //top一行判断
            }
        }


        //下边
        if (i - EnumHelper.ChessBoard.ChessWidth >= 0)//判断下面一行是否存在
        {
            if (arr[i - EnumHelper.ChessBoard.ChessWidth] != null) {
                if (arr[i].color == arr[i - EnumHelper.ChessBoard.ChessWidth].color) {
                    this.runLogic(arr, indexs, list, i - EnumHelper.ChessBoard.ChessWidth);
                }
                //bottom一行判断
            }
        }
    },
    tidyCube: function (arr) {
        for (var i = 0; i < EnumHelper.ChessBoard.ChessWidth; i++) {
            //判断i坐标上面有多少个null
            var count = 0;
            for (var a = i; a < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight; a = a + EnumHelper.ChessBoard.ChessWidth) {
                if (arr[a] == null) {
                    count++;
                }
            }

            for (var b = 0; b < count; b++) {
                this.runTidy(arr, i);
            }
        }
        return true;
    },
    runTidy: function (arr, i) {
        if (arr[i] == null) {
            //把上面的往下拉一次
            if (i + EnumHelper.ChessBoard.ChessWidth < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight) {
                arr[i] = arr[i + EnumHelper.ChessBoard.ChessWidth];
                arr[i + EnumHelper.ChessBoard.ChessWidth] = null;
                this.runTidy(arr, i + EnumHelper.ChessBoard.ChessWidth);
            }
        }
        else {
            if (i + EnumHelper.ChessBoard.ChessWidth < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight) {
                this.runTidy(arr, i + EnumHelper.ChessBoard.ChessWidth);
            }
        }
        return true;
    },
    /**
     * 添加顶部可点击的按钮
     * @param arr
     * @param addBoneList
     * @returns {boolean}
     */
    addTopBone: function (arr, addBoneList) {
        var i;
        var ca;
        var isAdd;

        if (arguments.length == 1) {
            for (i = 0; i < EnumHelper.ChessBoard.ChessWidth; i++) {
                isAdd = true;

                for (var a = i; a < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight; a = a + EnumHelper.ChessBoard.ChessWidth) {
                    if (arr[a] == null && isAdd == true)//基本条件
                    {
                        if (a < EnumHelper.ChessBoard.ChessWidth) {
                            //添加bone
                            ca = Object.create(CustomArray);
                            ca.color = 0;
                            ca.eff = EnumHelper.Effects.nullEff;
                            ca.statics = 0;
                            arr[a] = ca;
                            isAdd = false;
                        }
                        else {
                            if (arr[a - EnumHelper.ChessBoard.ChessWidth].color == 0)//这个添加点的下面一个不是可点击的
                            {
                                isAdd = false;
                            }
                            else {
                                //添加bone
                                ca = Object.create(CustomArray);
                                ca.color = 0;
                                ca.eff = EnumHelper.Effects.nullEff;
                                ca.statics = 0;
                                arr[a] = ca;
                                isAdd = false;
                            }
                        }
                    }
                }
            }
            return true;
        } else if (arguments.length == 2) {
            for (i = 0; i < EnumHelper.ChessBoard.ChessWidth; i++) {
                isAdd = true;

                for (a = i; a < EnumHelper.ChessBoard.ChessWidth * EnumHelper.ChessBoard.ChessHeight; a = a + EnumHelper.ChessBoard.ChessWidth) {
                    if (arr[a] == null && isAdd == true)//基本条件
                    {
                        if (a < EnumHelper.ChessBoard.ChessWidth) {
                            //添加bone
                            ca = Object.create(CustomArray);
                            ca.color = 0;
                            ca.eff = EnumHelper.Effects.nullEff;
                            ca.statics = 0;
                            arr[a] = ca;
                            addBoneList.push(a);
                            isAdd = false;
                        }
                        else {
                            if (arr[a - EnumHelper.ChessBoard.ChessWidth].color == 0)//这个添加点的下面一个不是可点击的
                            {
                                isAdd = false;
                            }
                            else {
                                //添加bone
                                ca = Object.create(CustomArray);
                                ca.color = 0;
                                ca.eff = EnumHelper.Effects.nullEff;
                                ca.statics = 0;
                                arr[a] = ca;
                                addBoneList.push(a);
                                isAdd = false;
                            }
                        }
                    }
                }
            }
            return true;
        }
    }
};

var LodingCube = {
    /**
     * 生成指定数量的色块
     * @param cubeArrayLength
     * @param isHaveBone
     * @param count 默认为1
     * @returns 如果参数为2,则返回CustomArray,如果参数为3,则返回包含CustomArray的数组
     */
    lodingCube: function (cubeArrayLength, isHaveBone, count) {
        var a;
        var ca;

        if (arguments.length == 2) {
            //色块长度判断
            var cal = LodingCube.calcCubeArrayLength(cubeArrayLength);

            a = Math.floor(cc.random0To1() * cal);
            //console.log(a + " : " + cal);
            if (isHaveBone == false && a == 0) {
                ++a;//可选三个不能为0的bone
            }
            ca = Object.create(CustomArray);
            ca.color = a;
            ca.statics = 0;
            ca.eff = EnumHelper.Effects.nullEff;
            return ca;
        }
        else if (arguments.length == 3) {
            //色块长度判断
            cal = LodingCube.calcCubeArrayLength(cubeArrayLength);

            if (count == null || count < 1) {
                count = 1;
            }
            //var arr = new std.vector<CustomArray*>();
            var arr = [];
            for (var i = 0; i < count; i++) {
                a = Math.floor(cc.random0To1() * cal);
                if (isHaveBone == false && a == 0) {
                    ++a;//可选三个不能为0的bone
                }
                ca = Object.create(CustomArray);
                ca.color = a;
                ca.statics = 0;
                ca.eff = EnumHelper.Effects.sixEff;
                arr.push(ca);
            }

            return arr;
        }
    },
    /**
     * 设置生成色块的长度
     * @param cubeArrayLength
     * @returns {*}
     */
    calcCubeArrayLength: function (cubeArrayLength) {
        if (cubeArrayLength == NaN || cubeArrayLength < EnumHelper.CommonEnum.minCubeArrayLength) {
            cubeArrayLength = EnumHelper.CommonEnum.minCubeArrayLength;
        }
        if (cubeArrayLength > EnumHelper.CommonEnum.maxCubeArrayLength) {
            cubeArrayLength = EnumHelper.CommonEnum.maxCubeArrayLength;
        }
        return cubeArrayLength;
    }
};