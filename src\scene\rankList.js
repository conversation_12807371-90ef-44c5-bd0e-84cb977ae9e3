/**
 * Created by <PERSON><PERSON> on 15/8/22.
 */

var RankListLayer = cc.Layer.extend({
    _root:null,
    ctor:function () {
        //////////////////////////////
        // 1. super init first
        this._super();

        /////////////////////////////
        // 2. add a menu item with "X" image, which is clicked to quit the program
        //    you may modify it.
        // ask the window size
        var size = cc.winSize;

        this._root = ccs.load(res.RankList_json).node;
        this.addChild(this._root, 0);

        var backBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.RankListEnum.btn_back);
        backBtn.addTouchEventListener(this.touchEvent, this);

        return true;
    },
    touchEvent: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        switch (sender.getTag()) {
            case EnumHelper.RankListEnum.btn_back:
                cc.director.runScene(new MainMenuScene());
                break;
            default:
                break;
        }
    }
});

var RankListScene = cc.Scene.extend({
    onEnter:function () {
        this._super();
        var layer = new RankListLayer();
        this.addChild(layer);
    }
});