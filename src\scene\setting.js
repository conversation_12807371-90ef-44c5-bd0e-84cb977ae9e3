/**
 * Created by <PERSON><PERSON> on 15/8/22.
 */

var SettingLayer = cc.Layer.extend({
    _root:null,
    ctor:function () {
        //////////////////////////////
        // 1. super init first
        this._super();

        /////////////////////////////
        // 2. add a menu item with "X" image, which is clicked to quit the program
        //    you may modify it.
        // ask the window size
        var size = cc.winSize;

        this._root = ccs.load(res.SettingMenu_json).node;
        this.addChild(this._root, 0);

        var backBtn = ccui.helper.seekWidgetByTag(this._root, EnumHelper.SettingEnum.backBtnSetting);
        backBtn.addTouchEventListener(this.touchEvent, this);

        return true;
    },
    touchEvent: function (sender, type) {
        if (type != ccui.Widget.TOUCH_ENDED) return;
        switch (sender.getTag()) {
            case EnumHelper.SettingEnum.backBtnSetting:
                cc.director.runScene(new MainMenuScene());
                break;
            default:
                break;
        }
    }
});

var SettingScene = cc.Scene.extend({
    onEnter:function () {
        this._super();
        var layer = new SettingLayer();
        this.addChild(layer);
    }
});