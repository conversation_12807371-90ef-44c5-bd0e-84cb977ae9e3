'use strict';
export const SPRITEATLAS = {
    "__type__": "cc.SpriteAtlas",
    "content": {
        "name": "",
        "spriteFrames": [],
    },
};

export class SpriteAtlas {

    static create() {
        return JSON.parse(JSON.stringify(SPRITEATLAS));
    }

    static async migrate(json2D: any) {
        const source = JSON.parse(JSON.stringify(SPRITEATLAS));
        for (const key in json2D) {
            const value = json2D[key];
            if (key === '__type__' || value === undefined || value === null) { continue; }
            source[key] = value;
        }
        return source;
    }

    static async apply(index: number, json2D: any, json3D: any) {
        const source = await SpriteAtlas.migrate(json2D[index]);
        json3D.splice(index, 1, source);
        return source;
    }
}
