
CCEffect %{
  techniques:
  - passes:
    - vert: vs:vert
      frag: fs:frag
      depthStencilState:
          depthTest: false
          depthWrite: false
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      rasterizerState:
        cullMode: none
}%

CCProgram vs %{
    precision highp float;
    #include <cc-global>
    in vec3 a_position;
    in vec4 a_color;
    in mediump vec2 a_uv0;

    out vec4 v_color;
    out mediump vec2 v_uv0;

    vec4 vert () {
      v_uv0 = a_uv0;
      v_color = a_color;
      return cc_matViewProj * vec4(a_position, 1);
    }
}%

CCProgram fs %{
  precision highp float;
  #include <texture>

  in mediump vec2 v_uv0;
  in vec4 v_color;

  #pragma builtin(local)
  layout(set = 2, binding = 10) uniform sampler2D cc_spriteTexture;

  vec4 frag () {
    vec4 color = v_color;
    CCTexture(cc_spriteTexture, v_uv0, color);
    float gray = 0.2126*color.r + 0.7152*color.g + 0.0722*color.b;
    return vec4(gray, gray, gray, color.a);
  }
}%
