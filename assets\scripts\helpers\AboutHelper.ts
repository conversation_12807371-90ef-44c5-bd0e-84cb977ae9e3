/**
 * 关于辅助类
 * 移植自原AboutHelper.js
 * <AUTHOR>
 */

import { _decorator, Node, Sprite, UITransform, Vec3, tween } from 'cc';
import { NodeHelper } from '../utils/NodeHelper';
import { RES } from '../utils/ResourceManager';
import ParticalHelper from './ParticalHelper';
import { Effects, ChessBoard, MainCommonEnum } from './EnumHelper';

const { ccclass } = _decorator;

interface ILayer {
    _root: Node;
    _playLayout: Node;
    threeCallBack?: () => void;
    fiveCallBack?: () => void;
    moveCube_ActionCallBack?: () => void;
    moveCube_Decline_CallBack?: () => void;
}

interface ICube {
    eff: Effects;
    color: number;
}

@ccclass('AboutHelper')
export default class AboutHelper {
    /**
     * 添加子立方体
     * @param widget 父节点
     * @param cubeName 立方体资源名称
     * @returns 创建的图像节点
     */
    static addChildCube(widget: Node | null, cubeName: string): Node | null {
        if (!widget) return null;

        // 创建新节点
        const imageNode = new Node('cube');
        const sprite = imageNode.addComponent(Sprite);
        const uiTransform = imageNode.addComponent(UITransform);

        // 加载纹理
        const spriteFrame = RES.GET_DOG(parseInt(cubeName));
        if (spriteFrame) {
            sprite.spriteFrame = spriteFrame;
        }

        // 设置位置到父节点中心
        const parentTransform = widget.getComponent(UITransform);
        if (parentTransform) {
            imageNode.position = new Vec3(
                parentTransform.width / 2,
                parentTransform.height / 2,
                0
            );
        }

        widget.addChild(imageNode);
        return imageNode;
    }

    /**
     * 三效果动作（有回调）
     * @param widget 目标节点
     * @param layer 层对象
     */
    static action_Three_HaveCallBack(widget: Node | null, layer: ILayer): void {
        if (!widget) return;

        const actImg = this.addChildCube(widget, "10");
        if (!actImg) return;

        // 设置初始透明度为0
        const sprite = actImg.getComponent(Sprite);
        if (sprite) {
            const color = sprite.color.clone();
            color.a = 0;
            sprite.color = color;
        }

        // 创建动画序列：淡入 -> 回调
        tween(actImg)
            .call(() => {
                if (sprite) {
                    const color = sprite.color.clone();
                    color.a = 0;
                    sprite.color = color;
                }
            })
            .to(0.1, {}, {
                onUpdate: (_target, ratio) => {
                    if (sprite) {
                        const color = sprite.color.clone();
                        color.a = Math.floor(255 * ratio);
                        sprite.color = color;
                    }
                }
            })
            .call(() => {
                if (layer.threeCallBack) {
                    layer.threeCallBack.call(layer);
                }
            })
            .start();
    }

    /**
     * 三效果动作（无回调）
     * @param widget 目标节点
     */
    static action_Three_NoCallBack(widget: Node | null): void {
        if (!widget) return;

        const actImg = this.addChildCube(widget, "10");
        if (!actImg) return;

        // 设置初始透明度为0
        const sprite = actImg.getComponent(Sprite);
        if (sprite) {
            const color = sprite.color.clone();
            color.a = 0;
            sprite.color = color;
        }

        // 创建淡入动画
        tween(actImg)
            .call(() => {
                if (sprite) {
                    const color = sprite.color.clone();
                    color.a = 0;
                    sprite.color = color;
                }
            })
            .to(0.1, {}, {
                onUpdate: (_target, ratio) => {
                    if (sprite) {
                        const color = sprite.color.clone();
                        color.a = Math.floor(255 * ratio);
                        sprite.color = color;
                    }
                }
            })
            .start();
    }

    /**
     * 四效果动作（有回调）
     * @param widget 移动的节点
     * @param targetWidget 目标节点
     * @param layer 层对象
     */
    static action_four_HaveCallBack(widget: Node | null, targetWidget: Node | null, layer: ILayer): void {
        if (!widget || !targetWidget) return;

        // 移动到目标位置 -> 回调
        tween(widget)
            .to(0.1, { position: targetWidget.position.clone() })
            .call(() => {
                if (layer.threeCallBack) {
                    layer.threeCallBack.call(layer);
                }
            })
            .start();
    }

    /**
     * 四效果动作（无回调）
     * @param widget 移动的节点
     * @param targetWidget 目标节点
     */
    static action_four_NoCallBack(widget: Node | null, targetWidget: Node | null): void {
        if (!widget || !targetWidget) return;

        const sprite = widget.getComponent(Sprite);

        // 移动到目标位置 -> 淡出
        tween(widget)
            .to(0.1, { position: targetWidget.position.clone() })
            .to(0.01, {}, {
                onUpdate: (_target, ratio) => {
                    if (sprite) {
                        const color = sprite.color.clone();
                        color.a = Math.floor(255 * (1 - ratio));
                        sprite.color = color;
                    }
                }
            })
            .start();
    }

    /**
     * 五效果动作（有回调）
     * @param tag 节点标签
     * @param layer 层对象
     */
    static action_five_HaveCallBack(tag: number, layer: ILayer): void {
        if (!layer._root) return;

        // 获取矩阵第一个位置
        const widget = NodeHelper.findChildByTagRecursive(layer._root, tag);
        if (!widget) return;

        const startCube = NodeHelper.findChildByTagRecursive(layer._root, MainCommonEnum.startPoint);
        if (!startCube) return;

        const position = startCube.position;
        const startTransform = startCube.getComponent(UITransform);
        if (!startTransform) return;

        const height = startTransform.height * startCube.scale.y;
        const width = startTransform.width * startCube.scale.x;

        const y = position.y - height / 2 + height * (ChessBoard.ChessHeight / 2);
        const x = position.x - width / 2 + width * ChessBoard.ChessWidth / 2;

        // 创建垂直光效
        const imgV = new Node('lightVertical');
        const spriteV = imgV.addComponent(Sprite);
        const transformV = imgV.addComponent(UITransform);

        // 加载垂直光效纹理
        const verticalSprite = RES.GET_DOG(11); // 假设11是垂直光效
        if (verticalSprite) {
            spriteV.spriteFrame = verticalSprite;
        }

        const scaleY = height * ChessBoard.ChessHeight / transformV.height;
        imgV.setScale(new Vec3(scaleY, scaleY, 1));
        imgV.position = new Vec3(widget.worldPosition.x, y, 0);
        layer._playLayout.addChild(imgV);

        // 垂直光效动画
        tween(imgV)
            .by(0.1, { scale: new Vec3(0.05, 0.05, 0) })
            .by(0.5, { scale: new Vec3(-0.5, -0.1, 0) })
            .call(() => {
                if (layer.fiveCallBack) {
                    layer.fiveCallBack.call(layer);
                }
            })
            .start();

        // 创建水平光效
        const imgAcross = new Node('lightHorizontal');
        const spriteH = imgAcross.addComponent(Sprite);
        imgAcross.addComponent(UITransform);

        // 加载水平光效纹理
        const horizontalSprite = RES.GET_DOG(12); // 假设12是水平光效
        if (horizontalSprite) {
            spriteH.spriteFrame = horizontalSprite;
        }

        imgAcross.setScale(new Vec3(scaleY, scaleY, 1));
        imgAcross.position = new Vec3(x, widget.position.y, 0);
        layer._playLayout.addChild(imgAcross);

        // 水平光效动画
        tween(imgAcross)
            .by(0.1, { scale: new Vec3(0.05, 0.05, 0) })
            .by(0.5, { scale: new Vec3(-0.1, -0.5, 0) })
            .start();
    }

    /**
     * 五效果动作（无回调）
     * @param layer 层对象
     * @param i 索引
     */
    static action_five_NoCallBack(layer: ILayer, i: number): void {
        // 加白色斑点的粒子特效
        const img = NodeHelper.findChildByTagRecursive(layer._root, 900 + i);
        if (img) {
            ParticalHelper.addStar({
                layer: layer._root,
                widget: img,
                particalName: "SnowParticle_plist", // 需要根据实际资源路径调整
                scaleF: 2.5
            });
        }
    }

    /**
     * 查找五效果被动触发
     * @param i 索引
     * @param arr 数组
     * @param list 列表
     * @param layer 层对象
     */
    static find_fivePassively(i: number, arr: (ICube | null)[], list: number[], layer: ILayer): void {
        // 检查是否已在列表中
        for (let index = 0; index < list.length; index++) {
            if (list[index] === i) {
                return;
            }
        }

        // 给i加上特效
        this.action_five_HaveCallBack(900 + i, layer);

        // 处理同行
        const row = Math.floor(i / ChessBoard.ChessWidth);
        for (let a = ChessBoard.ChessWidth * row; a < (row + 1) * ChessBoard.ChessWidth; a++) {
            if (arr[a] != null) {
                let inList = false;
                // 判断a是否已在list中
                for (let index = 0; index < list.length; index++) {
                    if (list[index] === a) {
                        inList = true;
                        break;
                    }
                }
                if (inList) {
                    continue;
                }

                if (arr[a] != null) {
                    list.push(a);
                    if (arr[a]!.eff === Effects.fiveEff) {
                        this.find_fivePassively(a, arr, list, layer);
                    }
                }
            }
        }

        // 处理同列
        let column = i % ChessBoard.ChessWidth;
        while (column < ChessBoard.ChessWidth * ChessBoard.ChessHeight) {
            if (arr[column] != null) {
                let inList = false;
                // 判断是否已在list中
                for (let index = 0; index < list.length; index++) {
                    if (list[index] === column) {
                        inList = true;
                        break;
                    }
                }
                if (inList) {
                    column += ChessBoard.ChessWidth;
                    continue;
                }

                if (arr[column] != null) {
                    list.push(column);
                    if (arr[column]!.eff === Effects.fiveEff) {
                        this.find_fivePassively(column, arr, list, layer);
                    }
                }
            }
            column += ChessBoard.ChessWidth;
        }
    }

    /**
     * 移动立方体动作（有回调）
     * @param second 持续时间（秒）
     * @param widget 移动的节点
     * @param targetPos 目标位置
     * @param layer 层对象
     */
    static action_moveCube_HaveCallBack(second: number, widget: Node | null, targetPos: Vec3, layer: ILayer): void {
        if (!widget) return;

        tween(widget)
            .to(second, { position: targetPos.clone() })
            .call(() => {
                if (layer.moveCube_ActionCallBack) {
                    layer.moveCube_ActionCallBack.call(layer);
                }
            })
            .start();
    }

    /**
     * 移动立方体动作（无回调）
     * @param second 持续时间（秒）
     * @param widget 移动的节点
     * @param targetPos 目标位置
     */
    static action_moveCube_NoHaveCallBack(second: number, widget: Node | null, targetPos: Vec3): void {
        if (!widget) return;

        tween(widget)
            .to(second, { position: targetPos.clone() })
            .start();
    }

    /**
     * 移动立方体下降动作（有回调）
     * @param widget 移动的节点
     * @param targetPos 目标位置
     * @param layer 层对象
     */
    static action_moveCube_Decline_HaveCallBack(widget: Node | null, targetPos: Vec3, layer: ILayer): void {
        if (!widget) return;

        tween(widget)
            .to(0.1, { position: targetPos.clone() })
            .call(() => {
                if (layer.moveCube_Decline_CallBack) {
                    layer.moveCube_Decline_CallBack.call(layer);
                }
            })
            .start();
    }

    /**
     * 移动立方体下降动作（无回调）
     * @param widget 移动的节点
     * @param targetPos 目标位置
     */
    static action_moveCube_Decline_NoCallBack(widget: Node | null, targetPos: Vec3): void {
        if (!widget) return;

        tween(widget)
            .to(0.1, { position: targetPos.clone() })
            .start();
    }
}