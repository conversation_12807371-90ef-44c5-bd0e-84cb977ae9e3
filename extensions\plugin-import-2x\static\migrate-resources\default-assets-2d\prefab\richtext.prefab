[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "richText", "_objFlags": 0, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_parent": null, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 153.38, "height": 50}, "_children": [], "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_tag": -1, "_opacityModifyRGB": false, "_id": "", "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "groupIndex": 0, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_useOriginalSize": true, "_N$string": "<color=#00ff00>Rich</c><color=#0fffff>Text</color>", "_N$horizontalAlign": 0, "_N$fontSize": 40, "_N$maxWidth": 0, "_N$lineHeight": 50}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4a37dd57-78cd-4cec-aad4-f11a73d12b63"}, "fileId": "fd4fe1WoCpJK6rVCZ13Z14R"}]