/**
 * 主菜单
 *
 * <AUTHOR>
 */

import { _decorator, Component, Node, Prefab, instantiate, But<PERSON>, director, find } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('MainMenuLayer')
export class MainMenuLayer extends Component {

    start() {
        
    }

    // 按钮点击事件处理
    protected onSettingBtnClick(): void {
        console.log('设置按钮被点击');
        // 加载设置场景
        director.loadScene('SettingScene');
    }

    protected onRankListBtnClick(): void {
        console.log('排行榜按钮被点击');
        // 加载排行榜场景
        director.loadScene('RankListScene');
    }

    protected onShopBtnClick(): void {
        console.log('商店按钮被点击');
        // 加载商店场景
        director.loadScene('ShopScene');
    }

    protected onJingdianBtnClick(): void {
        console.log('经典模式按钮被点击');
        // 加载经典模式场景
        director.loadScene('JingdianScene');
    }

}


